/**
 * Database Connection Configuration
 * Handles MongoDB connection with proper error handling and connection management
 */

import mongoose from "mongoose";
import logger from "../utils/logger.js";

// Connection state tracking
let isConnecting = false;

// Helper function to manage database connection with improved error handling
const connectDB = async () => {
  // Prevent multiple simultaneous connection attempts
  if (isConnecting) {
    logger.info("Database connection attempt already in progress...");
    return;
  }

  if (mongoose.connection.readyState === 1) {
    // Connection is already established
    logger.info("MongoDB connection is already active.");
    return;
  }

  try {
    isConnecting = true;
    mongoose.set("strictQuery", false);

    // Validate environment variable
    if (!process.env.MONGODB_URI) {
      throw new Error("MONGODB_URI environment variable is not defined");
    }

    await mongoose.connect(process.env.MONGODB_URI, {
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 10000, // Keep trying to send operations for 10 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
    });

    logger.info(`MongoDB connected successfully: ${mongoose.connection.host}`);

    // Set up connection event listeners
    setupConnectionListeners();

  } catch (error) {
    logger.error("Failed to connect to MongoDB:",  error);
    throw new Error(`Database connection error: ${error.message}`);
  } finally {
    isConnecting = false;
  }
};

// Setup connection event listeners for better monitoring
const setupConnectionListeners = () => {
  const db = mongoose.connection;

  db.on("error", (err) => {
    logger.error(`MongoDB connection error: ${err}`);
  });

  db.on("disconnected", () => {
    logger.warn("MongoDB disconnected. Attempting to reconnect...");
  });

  db.on("reconnected", () => {
    logger.info("MongoDB reconnected successfully");
  });

  db.on("close", () => {
    logger.info("MongoDB connection closed");
  });
};

// Database health check controller
export const checkDBHealth = async (req, res) => {
  const dbState = mongoose.connection.readyState;

  /**
   * MongoDB Connection States:
   * 0 = disconnected
   * 1 = connected
   * 2 = connecting
   * 3 = disconnecting
   */
  const status = {
    0: "Disconnected",
    1: "Connected",
    2: "Connecting",
    3: "Disconnecting",
  };

  try {
    // Check the connection state
    if (dbState === 1) {
      return res.status(200).json({
        status: "success",
        dbStatus: status[dbState],
        message: "Database connection is healthy.",
      });
    } else {
      return res.status(500).json({
        status: "fail",
        dbStatus: status[dbState],
        message: "Database is not connected properly.",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message: "An error occurred while checking the database connection.",
      error: error.message,
    });
  }
};

// Drop and reinitialize the database
//  const dropAndReinitializeDB = async () => {
//   try {
//     // Drop the current database
//     await mongoose.connection.dropDatabase();
//     logger.info("Database dropped successfully.");

//     // Close the current connection
//     await mongoose.connection.close();
//     logger.info("Database connection closed.");

//     // Reconnect to the database
//     await connectDB();
//     logger.info("Database reinitialized successfully.");
//   } catch (error) {
//     logger.error("Error during drop and reinitialize:", error);
//     throw new Error("Failed to drop and reinitialize the database.");
//   }
// };


export default connectDB;
