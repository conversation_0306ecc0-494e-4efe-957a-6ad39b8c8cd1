import { Redis } from "@upstash/redis";
import logger from "../utils/logger.js";

// Redis connection state
let isRedisAvailable = true;
let lastRedisCheck = 0;
const REDIS_CHECK_INTERVAL = 30000; // 30 seconds

// Create Redis client using Upstash with error handling
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL,
  token: process.env.UPSTASH_REDIS_REST_TOKEN,
});

// Test Redis connection
const testRedisConnection = async () => {
  try {
    await redis.ping();
    if (!isRedisAvailable) {
      logger.info("Redis connection restored");
      isRedisAvailable = true;
    }
    return true;
  } catch (error) {
    if (isRedisAvailable) {
      logger.error("Redis connection lost:", error.message);
      isRedisAvailable = false;
    }
    return false;
  }
};

// Check Redis availability with caching
const checkRedisAvailability = async () => {
  const now = Date.now();
  if (now - lastRedisCheck > REDIS_CHECK_INTERVAL) {
    lastRedisCheck = now;
    await testRedisConnection();
  }
  return isRedisAvailable;
};

// Session management functions with fallback
export const sessionService = {
  // Store session data with Redis fallback
  async setSession(sessionId, userData, expirationInSeconds = 86400) { // 24 hours default
    try {
      const available = await checkRedisAvailability();
      if (!available) {
        logger.warn("Redis unavailable, session storage skipped for:", sessionId);
        return false;
      }

      await redis.setex(
        `session:${sessionId}`,
        expirationInSeconds,
        JSON.stringify(userData)
      );
      return true;
    } catch (error) {
      logger.error("Error setting session:", error);
      isRedisAvailable = false;
      return false;
    }
  },

  // Get session data with Redis fallback
  async getSession(sessionId) {
    try {
      const available = await checkRedisAvailability();
      if (!available) {
        return null;
      }

      const sessionData = await redis.get(`session:${sessionId}`);
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      logger.error("Error getting session:", error);
      isRedisAvailable = false;
      return null;
    }
  },

  // Delete session
  async deleteSession(sessionId) {
    try {
      await redis.del(`session:${sessionId}`);
      return true;
    } catch (error) {
      console.error("Error deleting session:", error);
      return false;
    }
  },

  // Update session expiration
  async refreshSession(sessionId, expirationInSeconds = 86400) {
    try {
      await redis.expire(`session:${sessionId}`, expirationInSeconds);
      return true;
    } catch (error) {
      console.error("Error refreshing session:", error);
      return false;
    }
  },

  // Store temporary data (like email verification tokens)
  async setTempData(key, data, expirationInSeconds = 3600) { // 1 hour default
    try {
      await redis.setex(
        `temp:${key}`,
        expirationInSeconds,
        JSON.stringify(data)
      );
      return true;
    } catch (error) {
      console.error("Error setting temp data:", error);
      return false;
    }
  },

  // Get temporary data
  async getTempData(key) {
    try {
      const data = await redis.get(`temp:${key}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error("Error getting temp data:", error);
      return null;
    }
  },

  // Delete temporary data
  async deleteTempData(key) {
    try {
      await redis.del(`temp:${key}`);
      return true;
    } catch (error) {
      console.error("Error deleting temp data:", error);
      return false;
    }
  },

  // Rate limiting
  async checkRateLimit(identifier, maxRequests = 10, windowInSeconds = 60) {
    try {
      const key = `rate_limit:${identifier}`;
      const current = await redis.incr(key);
      
      if (current === 1) {
        await redis.expire(key, windowInSeconds);
      }
      
      return {
        allowed: current <= maxRequests,
        remaining: Math.max(0, maxRequests - current),
        resetTime: Date.now() + (windowInSeconds * 1000)
      };
    } catch (error) {
      console.error("Error checking rate limit:", error);
      return { allowed: true, remaining: maxRequests, resetTime: Date.now() + (windowInSeconds * 1000) };
    }
  },

  // Cache management
  async setCache(key, data, expirationInSeconds = 3600) {
    try {
      await redis.setex(
        `cache:${key}`,
        expirationInSeconds,
        JSON.stringify(data)
      );
      return true;
    } catch (error) {
      console.error("Error setting cache:", error);
      return false;
    }
  },

  async getCache(key) {
    try {
      const data = await redis.get(`cache:${key}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error("Error getting cache:", error);
      return null;
    }
  },

  async deleteCache(key) {
    try {
      await redis.del(`cache:${key}`);
      return true;
    } catch (error) {
      console.error("Error deleting cache:", error);
      return false;
    }
  }
};

export default redis;
