import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import { Strategy as GitHubStrategy } from "passport-github";
import User from "../models/user.model.js";
import logger from "../utils/logger.js";

// Helper function to link or create a user
const linkOrCreateUser = async (profile, provider) => {
  const { id, displayName, emails, photos } = profile;
  const email = emails?.[0]?.value; // Extract email if available
  logger.info(`[${provider.toUpperCase()}] Profile ID: ${id}, Email: ${email}`);

  try {
    // Check if a user exists with the provider ID in `oauthProviders`
    let user = await User.findOne({
      "oauthProviders.provider": provider,
      "oauthProviders.provider_id": id,
    });

    if (user) {
      logger.info(`[${provider.toUpperCase()}] User found by provider ID.`);
      return user;
    }

    // Check if a user exists with the email (if email is available)
    if (email) {
      user = await User.findOne({ email });

      if (user) {
        logger.info(
          `[${provider.toUpperCase()}] User found by email. Linking account.`
        );
        // Link the OAuth provider
        user.oauthProviders.push({
          provider,
          provider_id: id,
          linked_at: new Date(),
        });
        user.profileImage = user.profileImage || photos?.[0]?.value;
        user.isEmailVerified = true; // OAuth emails are verified
        await user.save();
        return user;
      }
    }

    // Generate username from email or displayName
    let generatedUsername = email?.split("@")[0]?.toLowerCase()?.replace(/[^\w]/g, "");
    if (!generatedUsername) {
      generatedUsername = displayName?.toLowerCase()?.replace(/[^\w]/g, "") || `user_${Date.now()}`;
    }

    // Ensure username is unique
    let uniqueUsername = generatedUsername;
    let counter = 1;
    while (await User.findOne({ username: uniqueUsername })) {
      uniqueUsername = `${generatedUsername}_${counter}`;
      counter++;
    }

    // No user found, create a new one
    const newUser = await User.create({
      username: uniqueUsername,
      firstName: profile.name?.givenName || displayName?.split(' ')[0] || '',
      lastName: profile.name?.familyName || displayName?.split(' ').slice(1).join(' ') || '',
      email: email || `${uniqueUsername}@oauth.local`, // Fallback email for GitHub users without public email
      profileImage: photos?.[0]?.value,
      isEmailVerified: !!email, // Only mark as verified if we have an actual email
      oauthProviders: [
        {
          provider,
          provider_id: id,
          linked_at: new Date(),
        },
      ],
    });

    logger.info(`[${provider.toUpperCase()}] New user created with username: ${uniqueUsername}`);
    return newUser;
  } catch (error) {
    logger.error(
      `[${provider.toUpperCase()}] Error during user linking/creation:`,
      error
    );
    throw new Error(`Error during user linking or creation: ${error.message}`);
  }
};

// Google OAuth Strategy (only if credentials are provided)
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: "/api/v1/auth/google/callback",
      },
      async (accessToken, refreshToken, profile, done) => {
        logger.info("[GOOGLE] Authenticating user...");
        try {
          const user = await linkOrCreateUser(profile, "google");
          return done(null, user);
        } catch (error) {
          logger.error("[GOOGLE] Authentication error:", error);
          return done(error, null);
        }
      }
    )
  );
} else {
  logger.log("Google OAuth not configured - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET not provided");
}

// GitHub OAuth Strategy (only if credentials are provided)
if (process.env.GITHUB_CLIENT_ID && process.env.GITHUB_CLIENT_SECRET) {
  passport.use(
    new GitHubStrategy(
      {
        clientID: process.env.GITHUB_CLIENT_ID,
        clientSecret: process.env.GITHUB_CLIENT_SECRET,
        callbackURL: "/api/v1/auth/github/callback",
      },
      async (accessToken, refreshToken, profile, done) => {
        logger.info("[GITHUB] Authenticating user...");
        try {
          const user = await linkOrCreateUser(profile, "github");
          return done(null, user);
        } catch (error) {
          logger.error("[GITHUB] Authentication error:", error);
          return done(error, null);
        }
      }
    )
  );
} else {
  logger.log("GitHub OAuth not configured - GITHUB_CLIENT_ID and GITHUB_CLIENT_SECRET not provided");
}

// Serialize User
passport.serializeUser((user, done) => {
  logger.info("Serializing user:", user.id);
  done(null, user.id);
});

// Deserialize User
passport.deserializeUser(async (id, done) => {
  logger.info("Deserializing user:", id);
  try {
    const user = await User.findById(id);
    done(null, user);
  } catch (error) {
    logger.error("Error during user deserialization:", error);
    done(error, null);
  }
});

export default passport;
