export const getStartOfDay = (date) => {
  const start = new Date(date);
  start.setHours(0, 0, 0, 0);
  return start;
};

export const getEndOfDay = (date) => {
  const end = new Date(date);
  end.setHours(23, 59, 59, 999);
  return end;
};

export const isSameDay = (date1, date2) => {
  const startOfDay1 = getStartOfDay(date1);
  const startOfDay2 = getStartOfDay(date2);
  return startOfDay1.getTime() === startOfDay2.getTime();
};
