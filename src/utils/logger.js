import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss.SSS'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack }) => {
      const logMessage = stack || message;
      return `${timestamp} ${process.env.NODE_ENV || 'development'} ${level.toUpperCase()} ${logMessage}`;
    })
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(({ level, message, timestamp, stack }) => {
          const logMessage = stack || message;
          return `${timestamp} ${process.env.NODE_ENV || 'development'} ${level} ${logMessage}`;
        })
      )
    }),
    new winston.transports.File({
      filename: 'logs/app.log',
      format: winston.format.combine(
        winston.format.uncolorize(),
        winston.format.printf(({ level, message, timestamp, stack }) => {
          const logMessage = stack || message;
          return `${timestamp} ${process.env.NODE_ENV || 'development'} ${level.toUpperCase()} ${logMessage}`;
        })
      )
    }),
  ],
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

export default logger;
