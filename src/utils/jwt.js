import jwt from 'jsonwebtoken';
import crypto from 'crypto';

/**
 * Generate a JWT token for the user with session tracking
 * @param {String} id - User ID
 * @param {Object} sessionData - Optional session data
 * @returns {Object} JWT token and session info
 */
export const generateToken = (id, sessionData = {}) => {
  const jwtTokenId = crypto.randomUUID(); // Unique identifier for this token
  const payload = {
    id,
    jti: jwtTokenId, // JWT ID for session tracking
    iat: Math.floor(Date.now() / 1000), // Issued at
    sessionData: {
      deviceFingerprint: sessionData.deviceFingerprint,
      ipAddress: sessionData.ipAddress
    }
  };

  const secret = process.env.JWT_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  const expiresIn = process.env.JWT_EXPIRES_IN || '30d';
  const token = jwt.sign(payload, secret, { expiresIn });

  // Calculate expiration date
  const expirationMs = expiresIn.includes('d')
    ? parseInt(expiresIn) * 24 * 60 * 60 * 1000
    : expiresIn.includes('h')
    ? parseInt(expiresIn) * 60 * 60 * 1000
    : parseInt(expiresIn) * 1000;

  return {
    token,
    jwtTokenId,
    expiresAt: new Date(Date.now() + expirationMs)
  };
};

/**
 * Generate email verification token
 * @returns {String} JWT token
 */
export const generateEmailVerificationToken = () => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  return jwt.sign({ purpose: "email_verification" }, secret, {
    expiresIn: "24h",
  });
};

/**
 * Generate password reset token
 * @returns {String} JWT token
 */
export const generatePasswordResetToken = () => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  return jwt.sign({ purpose: "password_reset" }, secret, {
    expiresIn: "1h",
  });
};

/**
 * Verify a JWT token
 * @param {String} token - JWT token
 * @returns {Object} Decoded payload
 */
export const verifyToken = (token) => {
  const secret = process.env.JWT_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  try {
    return jwt.verify(token, secret);
  } catch (err) {
    throw new Error('Token is invalid or expired');
  }
};

/**
 * Generate access and refresh tokens for OAuth flows
 * @param {String} userId - User ID
 * @param {Object} sessionData - Optional session data
 * @returns {Object} Access token data
 */
export const generateAccessAndRefreshTokens = async (userId, sessionData = {}) => {
  const tokenData = generateToken(userId, sessionData);
  
  return {
    accessToken: tokenData.token,
    jwtTokenId: tokenData.jwtTokenId,
    expiresAt: tokenData.expiresAt
  };
};

/**
 * Set access token cookie helper
 * @param {Response} res - Express response object
 * @param {String} accessToken - JWT access token
 */
export const setAccessTokenCookie = (res, accessToken) => {
  res.cookie("token", accessToken, {
    expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
  });
};
