import swaggerJSDoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";
/**
 * Swagger configuration
 */
const swaggerDefinition = {
  openapi: "3.0.0", // Swagger version
  info: {
    title: "Your API Name",
    version: "1.0.0",
    description: "API documentation for your app",
  },
  servers: [
    {
      url: `http://localhost:${process.env.PORT || 5000}/api`, // Base URL of your API
      description: "Local server",
    },
  ],
};

// ./controllers/user.controller.js
const options = {
  swaggerDefinition,
  // apis: ["./logger.js"], // Paths to your routes and controllers (for Swagger to read the JSDoc comments)
  //   apis: ["./controllers/*.js"], // Ensure this matches where your files are located
  // apis: ["./controllers/user.controller.js", "../swagger/*.js" , "./routes/*.js"], // Referencing the separate Swagger files
  apis: ["./src/swagger/*.js","./src/controllers/*.js"], // Referencing the separate Swagger files
};

// console.log("Swagger API paths:", options.apis); // Log paths to ensure correct files are being loaded

const swaggerSpec = swaggerJSDoc(options);
// console.log(swaggerSpec);

export { swaggerUi, swaggerSpec };
