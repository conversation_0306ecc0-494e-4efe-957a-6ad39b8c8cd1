import mongoose from "mongoose";

/**
 * Checks if an email is valid.
 * @param {string} email
 * @returns {boolean}
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Checks if a string is a valid MongoDB ObjectId.
 * @param {string} id
 * @returns {boolean}
 */
export const isValidObjectId = (id) => mongoose.Types.ObjectId.isValid(id);

/**
 * Checks if a value is a non-empty string.
 * @param {any} value
 * @returns {boolean}
 */
export const isNonEmptyString = (value) =>
  typeof value === "string" && value.trim().length > 0;
