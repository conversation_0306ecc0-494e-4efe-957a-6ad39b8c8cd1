export const ROLES = {
  USER: "user",
  ADMIN: "admin",
  SUPER_ADMIN: "super_admin",
  MODERATOR: "moderator",
  SALES: "sales",
  SUPPORT: "support",
  MARKETING: "marketing",
  ACCOUNTANT: "accountant",
  HR: "hr",
  DEVELOPER: "developer",
  <PERSON><PERSON><PERSON><PERSON><PERSON>: "designer",
};

// Product Types
export const PRODUCT_TYPES = {
  TEMPLATE: "templates",
  STARTER_KIT: "starter-kits",
  THEME: "themes",
  PLUGIN: "plugins",
  EXTENSION: "extensions",
  SCRIPT: "scripts",
  DESIGN_ASSET: "design-assets",
  E_BOOK: "e-books",
  COURSE: "courses",
  AUDIO: "audios",
  VIDEO: "videos",
  SOFTWARE: "softwares",
  OTHER: "others",
};
export const METADATA_TYPES = {
  TAG: "tag",
  TECH_STACK: "techStack",
  CATEGORY: "category",
  SUPER_CATEGORY: "superCategory",
  
};

export const PAYMENT_STATUSES = {
  PENDING: "pending",
  SUCCESSFUL: "successful",
  FAILED: "failed",
};

export const TRANSACTION_TYPES = {
  SUBSCRIPTION_PURCHASE: "subscription_purchase",
  SUBSCRIPTION_RENEWAL: "subscription_renewal",
  SUBSCRIPTION_UPGRADE: "subscription_upgrade",
  SUBSCRIPTION_DOWNGRADE: "subscription_downgrade",
  REFUND: "refund",
  CANCELLATION: "cancellation",
};

export const TRANSACTION_STATUSES = {
  PENDING: "pending",
  COMPLETED: "completed",
  FAILED: "failed",
  CANCELLED: "cancelled",
  REFUNDED: "refunded",
};

export const PAYMENT_METHODS = {
  CREDIT_CARD: "credit_card",
  DEBIT_CARD: "debit_card",
  CARD: "card", // Generic card payment
  PAYPAL: "paypal",
  STRIPE: "stripe",
  RAZORPAY: "razorpay",
  BANK_TRANSFER: "bank_transfer",
  UPI: "upi",
  WALLET: "wallet",
  FREE: "free", // For 100% coupon discounts
  UNKNOWN: "unknown", // For unknown payment methods
};

export const DEFAULT_CURRENCY = "USD";

export const PLAN_DURATIONS = {
  MONTHLY: "monthly",
  YEARLY: "yearly",
  LIFETIME: "lifetime",
};

export const HTTP_STATUS_CODES = {
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
};
