import crypto from 'crypto';
import { Webhook<PERSON>andler, WEBHOOK_EVENTS, SUBSCRIPTION_STATUS, PAYMENT_STATUS } from './WebhookHandler.js';

/**
 * Razorpay Webhook Handler
 * Handles Razorpay-specific webhook processing
 */
export class RazorpayWebhookHandler extends WebhookHandler {
  constructor() {
    super('razorpay');
  }

  /**
   * Verify Razorpay webhook signature
   * @param {Object} payload - Raw webhook payload
   * @param {string} signature - X-Razorpay-Signature header
   * @param {string} secret - Webhook secret
   * @returns {boolean} - Whether signature is valid
   */
  verifySignature(payload, signature, secret) {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(JSON.stringify(payload))
        .digest('hex');
      
      return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
      );
    } catch (error) {
      console.error('Razorpay signature verification error:', error);
      return false;
    }
  }

  /**
   * Parse Razorpay webhook payload
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Parsed payload
   */
  parsePayload(payload) {
    return payload;
  }

  /**
   * Get standardized event type from Razorpay webhook
   * @param {Object} payload - Raw webhook payload
   * @returns {string} - Standardized event type
   */
  getEventType(payload) {
    const { event } = payload;
    
    const eventMap = {
      // Subscription events
      'subscription.activated': WEBHOOK_EVENTS.SUBSCRIPTION_ACTIVATED,
      'subscription.charged': WEBHOOK_EVENTS.PAYMENT_SUCCESS,
      'subscription.completed': WEBHOOK_EVENTS.SUBSCRIPTION_EXPIRED,
      'subscription.cancelled': WEBHOOK_EVENTS.SUBSCRIPTION_CANCELLED,
      'subscription.paused': WEBHOOK_EVENTS.SUBSCRIPTION_PAUSED,
      'subscription.resumed': WEBHOOK_EVENTS.SUBSCRIPTION_RESUMED,
      'subscription.updated': WEBHOOK_EVENTS.SUBSCRIPTION_UPDATED,
      
      // Payment events
      'payment.captured': WEBHOOK_EVENTS.PAYMENT_SUCCESS,
      'payment.failed': WEBHOOK_EVENTS.PAYMENT_FAILED,
      'payment.authorized': WEBHOOK_EVENTS.PAYMENT_PENDING,
      
      // Invoice events
      'invoice.paid': WEBHOOK_EVENTS.INVOICE_PAID,
      'invoice.partially_paid': WEBHOOK_EVENTS.INVOICE_PAID,
      'invoice.payment_failed': WEBHOOK_EVENTS.INVOICE_FAILED,
      
      // Order events
      'order.paid': WEBHOOK_EVENTS.PAYMENT_SUCCESS,
      
      // Refund events
      'refund.created': WEBHOOK_EVENTS.PAYMENT_REFUNDED,
      'refund.speed_changed': WEBHOOK_EVENTS.PAYMENT_REFUNDED,
    };

    return eventMap[event] || WEBHOOK_EVENTS.UNKNOWN;
  }

  /**
   * Extract subscription data from Razorpay webhook
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized subscription data
   */
  extractSubscriptionData(payload) {
    const { payload: data } = payload;
    const subscription = data.subscription || {};
    const payment = data.payment || {};

    return {
      id: subscription.id,
      status: this.mapRazorpaySubscriptionStatus(subscription.status),
      planId: subscription.plan_id,
      planName: subscription.plan?.item?.name,
      customerId: subscription.customer_id,
      startDate: subscription.start_at ? new Date(subscription.start_at * 1000) : null,
      endDate: subscription.end_at ? new Date(subscription.end_at * 1000) : null,
      currentStart: subscription.current_start ? new Date(subscription.current_start * 1000) : null,
      currentEnd: subscription.current_end ? new Date(subscription.current_end * 1000) : null,
      chargeAt: subscription.charge_at ? new Date(subscription.charge_at * 1000) : null,
      totalCount: subscription.total_count,
      paidCount: subscription.paid_count,
      remainingCount: subscription.remaining_count,
      shortUrl: subscription.short_url,
      hasScheduledChanges: subscription.has_scheduled_changes,
      changeScheduledAt: subscription.change_scheduled_at ? new Date(subscription.change_scheduled_at * 1000) : null,
      notes: subscription.notes || {},
      raw: subscription,
    };
  }

  /**
   * Extract customer data from Razorpay webhook
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized customer data
   */
  extractCustomerData(payload) {
    const { payload: data } = payload;
    const subscription = data.subscription || {};
    const payment = data.payment || {};
    const order = data.order || {};

    // Customer data might be in different places depending on the event
    let customer = {};
    
    if (subscription.customer_id) {
      customer.externalId = subscription.customer_id;
    }
    
    if (payment.email) {
      customer.email = payment.email;
    }
    
    if (payment.contact) {
      customer.phone = payment.contact;
    }

    // Extract from notes if available
    if (subscription.notes) {
      customer.email = customer.email || subscription.notes.email;
      customer.name = customer.name || subscription.notes.customer_name;
      customer.phone = customer.phone || subscription.notes.phone;
    }

    if (payment.notes) {
      customer.email = customer.email || payment.notes.email;
      customer.name = customer.name || payment.notes.customer_name;
      customer.phone = customer.phone || payment.notes.phone;
    }

    return {
      externalId: customer.externalId,
      email: customer.email,
      name: customer.name,
      phone: customer.phone,
      raw: { subscription: subscription.customer_id, payment: payment.email },
    };
  }

  /**
   * Extract payment data from Razorpay webhook
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized payment data
   */
  extractPaymentData(payload) {
    const { payload: data } = payload;
    const payment = data.payment || {};
    const subscription = data.subscription || {};
    const invoice = data.invoice || {};
    const refund = data.refund || {};

    let amount = 0;
    let currency = 'INR';
    let status = PAYMENT_STATUS.PENDING;
    let method = 'unknown';
    let transactionId = null;

    // Extract from payment object
    if (payment.id) {
      amount = payment.amount ? payment.amount / 100 : 0; // Razorpay amounts are in paise
      currency = payment.currency || 'INR';
      status = this.mapRazorpayPaymentStatus(payment.status);
      method = payment.method || 'unknown';
      transactionId = payment.id;
    }
    
    // Extract from subscription object for subscription events
    else if (subscription.id) {
      // For subscription events, we might not have payment details
      // Use plan amount if available
      if (subscription.plan && subscription.plan.item) {
        amount = subscription.plan.item.amount ? subscription.plan.item.amount / 100 : 0;
        currency = subscription.plan.item.currency || 'INR';
      }
      status = subscription.status === 'active' ? PAYMENT_STATUS.SUCCESS : PAYMENT_STATUS.PENDING;
      transactionId = subscription.id;
    }

    // Extract from invoice
    else if (invoice.id) {
      amount = invoice.amount ? invoice.amount / 100 : 0;
      currency = invoice.currency || 'INR';
      status = invoice.status === 'paid' ? PAYMENT_STATUS.SUCCESS : PAYMENT_STATUS.FAILED;
      transactionId = invoice.id;
    }

    // Extract from refund
    else if (refund.id) {
      amount = refund.amount ? refund.amount / 100 : 0;
      currency = refund.currency || 'INR';
      status = PAYMENT_STATUS.REFUNDED;
      transactionId = refund.id;
    }

    return {
      transactionId,
      amount,
      originalAmount: amount, // Razorpay doesn't typically provide original amount in webhooks
      discountAmount: 0,
      currency,
      status,
      method,
      webhookId: payload.payload?.payment?.id || payload.payload?.subscription?.id,
      raw: { payment, subscription, invoice, refund },
    };
  }

  /**
   * Map Razorpay subscription status to standardized status
   * @param {string} razorpayStatus - Razorpay subscription status
   * @returns {string} - Standardized subscription status
   */
  mapRazorpaySubscriptionStatus(razorpayStatus) {
    const statusMap = {
      'created': SUBSCRIPTION_STATUS.PENDING,
      'authenticated': SUBSCRIPTION_STATUS.PENDING,
      'active': SUBSCRIPTION_STATUS.ACTIVE,
      'paused': SUBSCRIPTION_STATUS.PAUSED,
      'halted': SUBSCRIPTION_STATUS.PAUSED,
      'cancelled': SUBSCRIPTION_STATUS.CANCELLED,
      'completed': SUBSCRIPTION_STATUS.EXPIRED,
      'expired': SUBSCRIPTION_STATUS.EXPIRED,
    };

    return statusMap[razorpayStatus] || SUBSCRIPTION_STATUS.INACTIVE;
  }

  /**
   * Map Razorpay payment status to standardized status
   * @param {string} razorpayStatus - Razorpay payment status
   * @returns {string} - Standardized payment status
   */
  mapRazorpayPaymentStatus(razorpayStatus) {
    const statusMap = {
      'created': PAYMENT_STATUS.PENDING,
      'authorized': PAYMENT_STATUS.PENDING,
      'captured': PAYMENT_STATUS.SUCCESS,
      'refunded': PAYMENT_STATUS.REFUNDED,
      'failed': PAYMENT_STATUS.FAILED,
    };

    return statusMap[razorpayStatus] || PAYMENT_STATUS.PENDING;
  }

  /**
   * Get Razorpay webhook secret from environment
   * @returns {string} - Webhook secret
   */
  getWebhookSecret() {
    return process.env.RAZORPAY_WEBHOOK_SECRET;
  }
}

/**
 * Razorpay API Helper
 * Helper functions for Razorpay API operations
 */
export class RazorpayAPI {
  constructor() {
    this.keyId = process.env.RAZORPAY_KEY_ID;
    this.keySecret = process.env.RAZORPAY_KEY_SECRET;
    this.baseURL = 'https://api.razorpay.com/v1';
  }

  /**
   * Create subscription plan in Razorpay
   * @param {Object} planData - Plan data
   * @returns {Object} - Created plan
   */
  async createPlan(planData) {
    const { name, amount, currency = 'INR', interval, intervalCount = 1 } = planData;
    
    const payload = {
      period: interval, // daily, weekly, monthly, yearly
      interval: intervalCount,
      item: {
        name,
        amount: Math.round(amount * 100), // Convert to paise
        currency,
      },
    };

    return await this.makeRequest('POST', '/plans', payload);
  }

  /**
   * Create subscription in Razorpay
   * @param {Object} subscriptionData - Subscription data
   * @returns {Object} - Created subscription
   */
  async createSubscription(subscriptionData) {
    const {
      planId,
      customerId,
      totalCount,
      quantity = 1,
      startAt,
      expireBy,
      addons = [],
      notes = {},
      notifyInfo = {},
    } = subscriptionData;

    const payload = {
      plan_id: planId,
      customer_id: customerId,
      quantity,
      total_count: totalCount,
      start_at: startAt,
      expire_by: expireBy,
      addons,
      notes,
      notify_info: notifyInfo,
    };

    return await this.makeRequest('POST', '/subscriptions', payload);
  }

  /**
   * Make authenticated request to Razorpay API
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Object} - API response
   */
  async makeRequest(method, endpoint, data = null) {
    const auth = Buffer.from(`${this.keyId}:${this.keySecret}`).toString('base64');
    
    const options = {
      method,
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, options);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Razorpay API Error: ${error.error?.description || response.statusText}`);
    }

    return await response.json();
  }
}

// Create instances
export const razorpayWebhookHandler = new RazorpayWebhookHandler();
export const razorpayAPI = new RazorpayAPI();
