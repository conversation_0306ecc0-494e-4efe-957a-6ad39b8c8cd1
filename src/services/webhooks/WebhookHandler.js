/**
 * Base Webhook Handler Class
 * This abstract class defines the interface for all payment gateway webhook handlers
 */
export class WebhookHandler {
  constructor(gatewayName) {
    this.gatewayName = gatewayName;
  }

  /**
   * Verify webhook signature
   * @param {Object} payload - Raw webhook payload
   * @param {string} signature - Webhook signature
   * @param {string} secret - Webhook secret
   * @returns {boolean} - Whether signature is valid
   */
  verifySignature(payload, signature, secret) {
    throw new Error('verifySignature method must be implemented by subclass');
  }

  /**
   * Parse webhook payload into standardized format
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized webhook data
   */
  parsePayload(payload) {
    throw new Error('parsePayload method must be implemented by subclass');
  }

  /**
   * Get event type from webhook payload
   * @param {Object} payload - Raw webhook payload
   * @returns {string} - Standardized event type
   */
  getEventType(payload) {
    throw new Error('getEventType method must be implemented by subclass');
  }

  /**
   * Extract subscription data from webhook payload
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized subscription data
   */
  extractSubscriptionData(payload) {
    throw new Error('extractSubscriptionData method must be implemented by subclass');
  }

  /**
   * Extract customer data from webhook payload
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized customer data
   */
  extractCustomerData(payload) {
    throw new Error('extractCustomerData method must be implemented by subclass');
  }

  /**
   * Extract payment data from webhook payload
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized payment data
   */
  extractPaymentData(payload) {
    throw new Error('extractPaymentData method must be implemented by subclass');
  }

  /**
   * Process webhook event
   * @param {Object} payload - Raw webhook payload
   * @param {string} signature - Webhook signature
   * @returns {Object} - Processing result
   */
  async processWebhook(payload, signature) {
    try {
      // Verify signature
      const secret = this.getWebhookSecret();
      if (!this.verifySignature(payload, signature, secret)) {
        throw new Error('Invalid webhook signature');
      }

      // Parse payload
      const parsedData = this.parsePayload(payload);
      const eventType = this.getEventType(payload);

      // Extract relevant data
      const subscriptionData = this.extractSubscriptionData(payload);
      const customerData = this.extractCustomerData(payload);
      const paymentData = this.extractPaymentData(payload);

      return {
        success: true,
        gateway: this.gatewayName,
        eventType,
        data: {
          subscription: subscriptionData,
          customer: customerData,
          payment: paymentData,
          raw: parsedData,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        gateway: this.gatewayName,
      };
    }
  }

  /**
   * Get webhook secret for this gateway
   * @returns {string} - Webhook secret
   */
  getWebhookSecret() {
    throw new Error('getWebhookSecret method must be implemented by subclass');
  }
}

/**
 * Standardized Event Types
 * All gateways should map their events to these standard types
 */
export const WEBHOOK_EVENTS = {
  // Subscription events
  SUBSCRIPTION_CREATED: 'subscription.created',
  SUBSCRIPTION_ACTIVATED: 'subscription.activated',
  SUBSCRIPTION_UPDATED: 'subscription.updated',
  SUBSCRIPTION_CANCELLED: 'subscription.cancelled',
  SUBSCRIPTION_EXPIRED: 'subscription.expired',
  SUBSCRIPTION_PAUSED: 'subscription.paused',
  SUBSCRIPTION_RESUMED: 'subscription.resumed',
  
  // Payment events
  PAYMENT_SUCCESS: 'payment.success',
  PAYMENT_FAILED: 'payment.failed',
  PAYMENT_PENDING: 'payment.pending',
  PAYMENT_REFUNDED: 'payment.refunded',
  
  // Invoice events
  INVOICE_CREATED: 'invoice.created',
  INVOICE_PAID: 'invoice.paid',
  INVOICE_FAILED: 'invoice.failed',
  
  // Customer events
  CUSTOMER_CREATED: 'customer.created',
  CUSTOMER_UPDATED: 'customer.updated',
  
  // Plan events
  PLAN_CREATED: 'plan.created',
  PLAN_UPDATED: 'plan.updated',
  
  // Generic events
  TEST: 'test',
  UNKNOWN: 'unknown',
};

/**
 * Standardized Subscription Status
 */
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
  PAUSED: 'paused',
  PENDING: 'pending',
  TRIALING: 'trialing',
};

/**
 * Standardized Payment Status
 */
export const PAYMENT_STATUS = {
  SUCCESS: 'success',
  FAILED: 'failed',
  PENDING: 'pending',
  REFUNDED: 'refunded',
  PARTIALLY_REFUNDED: 'partially_refunded',
};

/**
 * Gateway Registry
 * Manages all registered webhook handlers
 */
export class WebhookRegistry {
  constructor() {
    this.handlers = new Map();
  }

  /**
   * Register a webhook handler
   * @param {string} gatewayName - Name of the payment gateway
   * @param {WebhookHandler} handler - Handler instance
   */
  register(gatewayName, handler) {
    this.handlers.set(gatewayName.toLowerCase(), handler);
  }

  /**
   * Get handler for a specific gateway
   * @param {string} gatewayName - Name of the payment gateway
   * @returns {WebhookHandler} - Handler instance
   */
  getHandler(gatewayName) {
    return this.handlers.get(gatewayName.toLowerCase());
  }

  /**
   * Get all registered gateways
   * @returns {Array} - Array of gateway names
   */
  getRegisteredGateways() {
    return Array.from(this.handlers.keys());
  }

  /**
   * Process webhook for a specific gateway
   * @param {string} gatewayName - Name of the payment gateway
   * @param {Object} payload - Webhook payload
   * @param {string} signature - Webhook signature
   * @returns {Object} - Processing result
   */
  async processWebhook(gatewayName, payload, signature) {
    const handler = this.getHandler(gatewayName);
    if (!handler) {
      return {
        success: false,
        error: `No handler registered for gateway: ${gatewayName}`,
      };
    }

    return await handler.processWebhook(payload, signature);
  }
}

// Create global registry instance
export const webhookRegistry = new WebhookRegistry();
