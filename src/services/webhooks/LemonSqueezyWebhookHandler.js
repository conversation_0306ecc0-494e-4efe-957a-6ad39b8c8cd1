import crypto from 'crypto';
import { <PERSON>ho<PERSON><PERSON><PERSON><PERSON>, WEBHOOK_EVENTS, SUBSCRIPTION_STATUS, PAYMENT_STATUS } from './WebhookHandler.js';

/**
 * LemonSqueezy Webhook Handler
 * Example implementation for LemonSqueezy payment gateway
 */
export class LemonSqueezyWebhookHandler extends WebhookHandler {
  constructor() {
    super('lemonsqueezy');
  }

  /**
   * Verify LemonSqueezy webhook signature
   * @param {Object} payload - Raw webhook payload
   * @param {string} signature - X-Signature header
   * @param {string} secret - Webhook secret
   * @returns {boolean} - Whether signature is valid
   */
  verifySignature(payload, signature, secret) {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(JSON.stringify(payload))
        .digest('hex');
      
      return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
      );
    } catch (error) {
      console.error('LemonSqueezy signature verification error:', error);
      return false;
    }
  }

  /**
   * Parse LemonSqueezy webhook payload
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Parsed payload
   */
  parsePayload(payload) {
    return payload;
  }

  /**
   * Get standardized event type from LemonSqueezy webhook
   * @param {Object} payload - Raw webhook payload
   * @returns {string} - Standardized event type
   */
  getEventType(payload) {
    const eventName = payload.meta?.event_name;
    
    const eventMap = {
      // Subscription events
      'subscription_created': WEBHOOK_EVENTS.SUBSCRIPTION_CREATED,
      'subscription_updated': WEBHOOK_EVENTS.SUBSCRIPTION_UPDATED,
      'subscription_cancelled': WEBHOOK_EVENTS.SUBSCRIPTION_CANCELLED,
      'subscription_resumed': WEBHOOK_EVENTS.SUBSCRIPTION_RESUMED,
      'subscription_expired': WEBHOOK_EVENTS.SUBSCRIPTION_EXPIRED,
      'subscription_paused': WEBHOOK_EVENTS.SUBSCRIPTION_PAUSED,
      'subscription_unpaused': WEBHOOK_EVENTS.SUBSCRIPTION_RESUMED,
      
      // Payment events
      'subscription_payment_success': WEBHOOK_EVENTS.PAYMENT_SUCCESS,
      'subscription_payment_failed': WEBHOOK_EVENTS.PAYMENT_FAILED,
      'subscription_payment_recovered': WEBHOOK_EVENTS.PAYMENT_SUCCESS,
      
      // Order events
      'order_created': WEBHOOK_EVENTS.PAYMENT_PENDING,
      'order_refunded': WEBHOOK_EVENTS.PAYMENT_REFUNDED,
    };

    return eventMap[eventName] || WEBHOOK_EVENTS.UNKNOWN;
  }

  /**
   * Extract subscription data from LemonSqueezy webhook
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized subscription data
   */
  extractSubscriptionData(payload) {
    const data = payload.data;
    const attributes = data?.attributes || {};

    return {
      id: data?.id,
      status: this.mapLemonSqueezySubscriptionStatus(attributes.status),
      planId: attributes.variant_id,
      planName: attributes.variant_name,
      customerId: attributes.customer_id,
      startDate: attributes.created_at ? new Date(attributes.created_at) : null,
      endDate: attributes.ends_at ? new Date(attributes.ends_at) : null,
      renewsAt: attributes.renews_at ? new Date(attributes.renews_at) : null,
      trialEndsAt: attributes.trial_ends_at ? new Date(attributes.trial_ends_at) : null,
      pausedAt: attributes.paused_at ? new Date(attributes.paused_at) : null,
      cancelledAt: attributes.cancelled_at ? new Date(attributes.cancelled_at) : null,
      productId: attributes.product_id,
      productName: attributes.product_name,
      userEmail: attributes.user_email,
      userName: attributes.user_name,
      cardBrand: attributes.card_brand,
      cardLastFour: attributes.card_last_four,
      raw: data,
    };
  }

  /**
   * Extract customer data from LemonSqueezy webhook
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized customer data
   */
  extractCustomerData(payload) {
    const data = payload.data;
    const attributes = data?.attributes || {};

    return {
      externalId: attributes.customer_id,
      email: attributes.user_email,
      name: attributes.user_name,
      phone: null, // LemonSqueezy doesn't typically provide phone
      raw: attributes,
    };
  }

  /**
   * Extract payment data from LemonSqueezy webhook
   * @param {Object} payload - Raw webhook payload
   * @returns {Object} - Standardized payment data
   */
  extractPaymentData(payload) {
    const data = payload.data;
    const attributes = data?.attributes || {};
    const eventName = payload.meta?.event_name;

    let status = PAYMENT_STATUS.PENDING;
    if (eventName?.includes('success')) {
      status = PAYMENT_STATUS.SUCCESS;
    } else if (eventName?.includes('failed')) {
      status = PAYMENT_STATUS.FAILED;
    } else if (eventName?.includes('refunded')) {
      status = PAYMENT_STATUS.REFUNDED;
    }

    return {
      transactionId: data?.id,
      amount: attributes.subtotal ? attributes.subtotal / 100 : 0, // Convert from cents
      originalAmount: attributes.total ? attributes.total / 100 : 0,
      discountAmount: attributes.discount_total ? attributes.discount_total / 100 : 0,
      currency: attributes.currency || 'USD',
      status,
      method: attributes.card_brand || 'card',
      webhookId: payload.meta?.webhook_id,
      raw: data,
    };
  }

  /**
   * Map LemonSqueezy subscription status to standardized status
   * @param {string} lemonSqueezyStatus - LemonSqueezy subscription status
   * @returns {string} - Standardized subscription status
   */
  mapLemonSqueezySubscriptionStatus(lemonSqueezyStatus) {
    const statusMap = {
      'on_trial': SUBSCRIPTION_STATUS.TRIALING,
      'active': SUBSCRIPTION_STATUS.ACTIVE,
      'paused': SUBSCRIPTION_STATUS.PAUSED,
      'cancelled': SUBSCRIPTION_STATUS.CANCELLED,
      'expired': SUBSCRIPTION_STATUS.EXPIRED,
      'past_due': SUBSCRIPTION_STATUS.INACTIVE,
      'unpaid': SUBSCRIPTION_STATUS.INACTIVE,
    };

    return statusMap[lemonSqueezyStatus] || SUBSCRIPTION_STATUS.INACTIVE;
  }

  /**
   * Get LemonSqueezy webhook secret from environment
   * @returns {string} - Webhook secret
   */
  getWebhookSecret() {
    return process.env.LEMONSQUEEZY_WEBHOOK_SECRET;
  }
}

export const lemonSqueezyWebhookHandler = new LemonSqueezyWebhookHandler();

/**
 * Paddle Webhook Handler
 * Example implementation for Paddle payment gateway
 */
export class PaddleWebhookHandler extends WebhookHandler {
  constructor() {
    super('paddle');
  }

  /**
   * Verify Paddle webhook signature
   * @param {Object} payload - Raw webhook payload
   * @param {string} signature - Paddle-Signature header
   * @param {string} secret - Webhook secret
   * @returns {boolean} - Whether signature is valid
   */
  verifySignature(payload, signature, secret) {
    try {
      // Paddle uses a different signature format
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(JSON.stringify(payload))
        .digest('hex');

      return signature === expectedSignature;
    } catch (error) {
      console.error('Paddle signature verification error:', error);
      return false;
    }
  }

  parsePayload(payload) {
    return payload;
  }

  getEventType(payload) {
    const eventType = payload.event_type;

    const eventMap = {
      'subscription.created': WEBHOOK_EVENTS.SUBSCRIPTION_CREATED,
      'subscription.updated': WEBHOOK_EVENTS.SUBSCRIPTION_UPDATED,
      'subscription.cancelled': WEBHOOK_EVENTS.SUBSCRIPTION_CANCELLED,
      'subscription.paused': WEBHOOK_EVENTS.SUBSCRIPTION_PAUSED,
      'subscription.resumed': WEBHOOK_EVENTS.SUBSCRIPTION_RESUMED,
      'transaction.completed': WEBHOOK_EVENTS.PAYMENT_SUCCESS,
      'transaction.payment_failed': WEBHOOK_EVENTS.PAYMENT_FAILED,
    };

    return eventMap[eventType] || WEBHOOK_EVENTS.UNKNOWN;
  }

  extractSubscriptionData(payload) {
    const data = payload.data;

    return {
      id: data?.id,
      status: this.mapPaddleSubscriptionStatus(data?.status),
      planId: data?.price?.id,
      planName: data?.price?.name,
      customerId: data?.customer?.id,
      startDate: data?.started_at ? new Date(data.started_at) : null,
      endDate: data?.next_billed_at ? new Date(data.next_billed_at) : null,
      raw: data,
    };
  }

  extractCustomerData(payload) {
    const customer = payload.data?.customer;

    return {
      externalId: customer?.id,
      email: customer?.email,
      name: customer?.name,
      phone: customer?.phone,
      raw: customer,
    };
  }

  extractPaymentData(payload) {
    const data = payload.data;

    return {
      transactionId: data?.id,
      amount: data?.details?.totals?.total ? data.details.totals.total / 100 : 0,
      currency: data?.currency_code || 'USD',
      status: data?.status === 'completed' ? PAYMENT_STATUS.SUCCESS : PAYMENT_STATUS.FAILED,
      method: 'card',
      raw: data,
    };
  }

  mapPaddleSubscriptionStatus(paddleStatus) {
    const statusMap = {
      'active': SUBSCRIPTION_STATUS.ACTIVE,
      'canceled': SUBSCRIPTION_STATUS.CANCELLED,
      'paused': SUBSCRIPTION_STATUS.PAUSED,
      'past_due': SUBSCRIPTION_STATUS.INACTIVE,
      'trialing': SUBSCRIPTION_STATUS.TRIALING,
    };

    return statusMap[paddleStatus] || SUBSCRIPTION_STATUS.INACTIVE;
  }

  getWebhookSecret() {
    return process.env.PADDLE_WEBHOOK_SECRET;
  }
}

export const paddleWebhookHandler = new PaddleWebhookHandler();
