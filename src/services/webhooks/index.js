/**
 * Webhook Service Initialization
 * Register all webhook handlers and export the registry
 */

import { webhookRegistry } from './WebhookHandler.js';
import { razorpayWebhookHandler } from './RazorpayWebhookHandler.js';
import { 
  lemonSqueezyWebhookHandler, 
  paddleWebhookHandler 
} from './LemonSqueezyWebhookHandler.js';

/**
 * Initialize webhook handlers
 * Register all available payment gateway handlers
 */
export function initializeWebhookHandlers() {
  console.log('Initializing webhook handlers...');

  // Register Razorpay handler
  webhookRegistry.register('razorpay', razorpayWebhookHandler);
  console.log('✅ Razorpay webhook handler registered');

  // Register LemonSqueezy handler
  webhookRegistry.register('lemonsqueezy', lemonSqueezyWebhookHandler);
  console.log('✅ LemonSqueezy webhook handler registered');

  // Register Paddle handler
  webhookRegistry.register('paddle', paddleWebhookHandler);
  console.log('✅ Paddle webhook handler registered');

  // Add more handlers here as needed
  // webhookRegistry.register('stripe', stripeWebhookHandler);
  // webhookRegistry.register('paypal', paypalWebhookHandler);

  const registeredGateways = webhookRegistry.getRegisteredGateways();
  console.log(`🎉 Webhook system initialized with ${registeredGateways.length} gateways:`, registeredGateways);
}

/**
 * Validate webhook configuration
 * Check if all required environment variables are set
 */
export function validateWebhookConfiguration() {
  console.log('Validating webhook configuration...');

  const requiredEnvVars = [
    'RAZORPAY_WEBHOOK_SECRET',
    // Add other required env vars as you implement more gateways
    // 'LEMONSQUEEZY_WEBHOOK_SECRET',
    // 'PADDLE_WEBHOOK_SECRET',
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.warn('⚠️  Missing webhook environment variables:', missingVars);
    console.warn('Some webhook handlers may not work properly');
  } else {
    console.log('✅ All webhook environment variables configured');
  }

  // Validate webhook secrets are not empty
  const secrets = {
    razorpay: process.env.RAZORPAY_WEBHOOK_SECRET,
    lemonsqueezy: process.env.LEMONSQUEEZY_WEBHOOK_SECRET,
    paddle: process.env.PADDLE_WEBHOOK_SECRET,
  };

  Object.entries(secrets).forEach(([gateway, secret]) => {
    if (secret && secret.length < 10) {
      console.warn(`⚠️  Webhook secret for ${gateway} seems too short`);
    }
  });
}

/**
 * Get webhook configuration summary
 * Returns current webhook system configuration
 */
export function getWebhookConfiguration() {
  return {
    registeredGateways: webhookRegistry.getRegisteredGateways(),
    environment: process.env.NODE_ENV,
    configuration: {
      razorpay: {
        enabled: !!process.env.RAZORPAY_WEBHOOK_SECRET,
        keyConfigured: !!process.env.RAZORPAY_KEY_ID,
      },
      lemonsqueezy: {
        enabled: !!process.env.LEMONSQUEEZY_WEBHOOK_SECRET,
      },
      paddle: {
        enabled: !!process.env.PADDLE_WEBHOOK_SECRET,
      },
    },
  };
}

// Export the registry for use in other parts of the application
export { webhookRegistry };

// Export handlers for direct use if needed
export {
  razorpayWebhookHandler,
  lemonSqueezyWebhookHandler,
  paddleWebhookHandler,
};
