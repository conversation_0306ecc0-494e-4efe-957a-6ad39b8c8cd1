/**
 * Webhook Service Initialization
 * Register all webhook handlers and export the registry
 */

import { webhookRegistry } from './WebhookHandler.js';
import { razorpayWebhookHandler } from './RazorpayWebhookHandler.js';
import {
  lemonSqueezyWebhookHandler,
  paddleWebhookHandler
} from './LemonSqueezyWebhookHandler.js';
import logger from '../../utils/logger.js';

/**
 * Initialize webhook handlers
 * Register all available payment gateway handlers
 */
export function initializeWebhookHandlers() {
  logger.info('Initializing webhook handlers...');

  // Register Razorpay handler
  webhookRegistry.register('razorpay', razorpayWebhookHandler);
  logger.info('✅ Razorpay webhook handler registered');

  // Register LemonSqueezy handler
  webhookRegistry.register('lemonsqueezy', lemonSqueezyWebhookHandler);
  logger.info('✅ LemonSqueezy webhook handler registered');

  // Register Paddle handler
  webhookRegistry.register('paddle', paddleWebhookHandler);
  logger.info('✅ Paddle webhook handler registered');

  // Add more handlers here as needed
  // webhookRegistry.register('stripe', stripeWebhookHandler);
  // webhookRegistry.register('paypal', paypalWebhookHandler);

  const registeredGateways = webhookRegistry.getRegisteredGateways();
  logger.info(`🎉 Webhook system initialized with ${registeredGateways.length} gateways: [ ${registeredGateways.map(g => `'${g}'`).join(', ')} ]`);
}

/**
 * Validate webhook configuration
 * Check if all required environment variables are set
 */
export function validateWebhookConfiguration() {
  logger.info('Validating webhook configuration...');

  const requiredEnvVars = [
    'RAZORPAY_WEBHOOK_SECRET',
    // Add other required env vars as you implement more gateways
    // 'LEMONSQUEEZY_WEBHOOK_SECRET',
    // 'PADDLE_WEBHOOK_SECRET',
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    logger.warn(`⚠️  Missing webhook environment variables: ${missingVars.join(', ')}`);
    logger.warn('Some webhook handlers may not work properly');
  } else {
    logger.info('✅ All webhook environment variables configured');
  }

  // Validate webhook secrets are not empty
  const secrets = {
    razorpay: process.env.RAZORPAY_WEBHOOK_SECRET,
    lemonsqueezy: process.env.LEMONSQUEEZY_WEBHOOK_SECRET,
    paddle: process.env.PADDLE_WEBHOOK_SECRET,
  };

  Object.entries(secrets).forEach(([gateway, secret]) => {
    if (secret && secret.length < 10) {
      logger.warn(`⚠️  Webhook secret for ${gateway} seems too short`);
    }
  });
}

/**
 * Get webhook configuration summary
 * Returns current webhook system configuration
 */
export function getWebhookConfiguration() {
  return {
    registeredGateways: webhookRegistry.getRegisteredGateways(),
    environment: process.env.NODE_ENV,
    configuration: {
      razorpay: {
        enabled: !!process.env.RAZORPAY_WEBHOOK_SECRET,
        keyConfigured: !!process.env.RAZORPAY_KEY_ID,
      },
      lemonsqueezy: {
        enabled: !!process.env.LEMONSQUEEZY_WEBHOOK_SECRET,
      },
      paddle: {
        enabled: !!process.env.PADDLE_WEBHOOK_SECRET,
      },
    },
  };
}

// Export the registry for use in other parts of the application
export { webhookRegistry };

// Export handlers for direct use if needed
export {
  razorpayWebhookHandler,
  lemonSqueezyWebhookHandler,
  paddleWebhookHandler,
};
