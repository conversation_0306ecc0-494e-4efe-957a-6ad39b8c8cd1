import Plan, { PLAN_TYPES, PLAN_TIERS } from "../../models/plan.model.js";
import logger from "../../utils/logger.js";

/**
 * Plan Business Service
 * Handles business logic for subscription tiers and plan management
 */
export class PlanBusinessService {
  
  /**
   * Initialize default plans for the business model
   * Creates Free, Individual, and Startup plans with monthly/yearly options
   */
  static async initializeDefaultPlans() {
    try {
      const existingPlans = await Plan.countDocuments();
      if (existingPlans > 0) {
        logger.info("Plans already exist, skipping initialization");
        return;
      }

      const defaultPlans = [
        // Free Plan
        {
          name: "Free",
          description: "Perfect for getting started with basic features",
          price: 0,
          currency: "USD",
          duration: "lifetime",
          planType: PLAN_TYPES.FREE,
          tier: PLAN_TIERS.FREE,
          isActive: true,
          isVisible: true,
          isFeatured: false,
          sortOrder: 1,
          features: [
            { name: "Basic Templates", description: "Access to 10 basic templates", included: true, limit: 10 },
            { name: "Standard Support", description: "Email support", included: true },
            { name: "Personal Use", description: "For personal projects only", included: true }
          ],
          limits: {
            downloads: 5,
            projects: 3,
            storage: 100, // 100MB
            apiCalls: 100,
            teamMembers: 1
          },
          benefits: {
            btn: { text: "Get Started Free", variant: "outline" },
            features: [
              { text: "5 downloads per month", included: true },
              { text: "3 active projects", included: true },
              { text: "100MB storage", included: true },
              { text: "Email support", included: true },
              { text: "Commercial license", included: false },
              { text: "Priority support", included: false }
            ]
          }
        },

        // Individual Monthly
        {
          name: "Individual Monthly",
          description: "Perfect for freelancers and individual creators",
          price: 19.99,
          currency: "USD",
          duration: "monthly",
          planType: PLAN_TYPES.INDIVIDUAL,
          tier: PLAN_TIERS.PRO,
          isActive: true,
          isVisible: true,
          isFeatured: true,
          sortOrder: 2,
          features: [
            { name: "Premium Templates", description: "Access to all premium templates", included: true, limit: -1 },
            { name: "Priority Support", description: "Priority email and chat support", included: true },
            { name: "Commercial License", description: "Use for commercial projects", included: true },
            { name: "Advanced Tools", description: "Access to advanced design tools", included: true }
          ],
          limits: {
            downloads: 100,
            projects: 25,
            storage: 5000, // 5GB
            apiCalls: 1000,
            teamMembers: 1
          },
          benefits: {
            btn: { text: "Start Individual Plan", variant: "default" },
            features: [
              { text: "100 downloads per month", included: true },
              { text: "25 active projects", included: true },
              { text: "5GB storage", included: true },
              { text: "Priority support", included: true },
              { text: "Commercial license", included: true },
              { text: "Advanced tools", included: true }
            ]
          }
        },

        // Individual Yearly (with discount)
        {
          name: "Individual Yearly",
          description: "Best value for individual creators - 2 months free!",
          price: 199.99, // ~17% discount
          currency: "USD",
          duration: "yearly",
          planType: PLAN_TYPES.INDIVIDUAL,
          tier: PLAN_TIERS.PRO,
          isActive: true,
          isVisible: true,
          isFeatured: false,
          sortOrder: 3,
          features: [
            { name: "Premium Templates", description: "Access to all premium templates", included: true, limit: -1 },
            { name: "Priority Support", description: "Priority email and chat support", included: true },
            { name: "Commercial License", description: "Use for commercial projects", included: true },
            { name: "Advanced Tools", description: "Access to advanced design tools", included: true }
          ],
          limits: {
            downloads: 100,
            projects: 25,
            storage: 5000, // 5GB
            apiCalls: 1000,
            teamMembers: 1
          },
          benefits: {
            btn: { text: "Save with Yearly", variant: "default" },
            features: [
              { text: "100 downloads per month", included: true },
              { text: "25 active projects", included: true },
              { text: "5GB storage", included: true },
              { text: "Priority support", included: true },
              { text: "Commercial license", included: true },
              { text: "Advanced tools", included: true },
              { text: "2 months free!", included: true, highlight: true }
            ]
          }
        },

        // Startup Monthly
        {
          name: "Startup Monthly",
          description: "Perfect for growing teams and businesses",
          price: 49.99,
          currency: "USD",
          duration: "monthly",
          planType: PLAN_TYPES.STARTUP,
          tier: PLAN_TIERS.PRO,
          isActive: true,
          isVisible: true,
          isFeatured: false,
          sortOrder: 4,
          features: [
            { name: "Everything in Individual", description: "All Individual plan features", included: true },
            { name: "Team Collaboration", description: "Collaborate with team members", included: true },
            { name: "Brand Kit", description: "Custom brand kit and assets", included: true },
            { name: "API Access", description: "Full API access for integrations", included: true },
            { name: "Analytics Dashboard", description: "Detailed usage analytics", included: true }
          ],
          limits: {
            downloads: 500,
            projects: 100,
            storage: 25000, // 25GB
            apiCalls: 10000,
            teamMembers: 10
          },
          benefits: {
            btn: { text: "Start Startup Plan", variant: "default" },
            features: [
              { text: "500 downloads per month", included: true },
              { text: "100 active projects", included: true },
              { text: "25GB storage", included: true },
              { text: "Up to 10 team members", included: true },
              { text: "API access", included: true },
              { text: "Analytics dashboard", included: true },
              { text: "Brand kit", included: true }
            ]
          }
        },

        // Startup Yearly (with discount)
        {
          name: "Startup Yearly",
          description: "Best value for growing teams - 2 months free!",
          price: 499.99, // ~17% discount
          currency: "USD",
          duration: "yearly",
          planType: PLAN_TYPES.STARTUP,
          tier: PLAN_TIERS.PRO,
          isActive: true,
          isVisible: true,
          isFeatured: false,
          sortOrder: 5,
          features: [
            { name: "Everything in Individual", description: "All Individual plan features", included: true },
            { name: "Team Collaboration", description: "Collaborate with team members", included: true },
            { name: "Brand Kit", description: "Custom brand kit and assets", included: true },
            { name: "API Access", description: "Full API access for integrations", included: true },
            { name: "Analytics Dashboard", description: "Detailed usage analytics", included: true }
          ],
          limits: {
            downloads: 500,
            projects: 100,
            storage: 25000, // 25GB
            apiCalls: 10000,
            teamMembers: 10
          },
          benefits: {
            btn: { text: "Save with Yearly", variant: "default" },
            features: [
              { text: "500 downloads per month", included: true },
              { text: "100 active projects", included: true },
              { text: "25GB storage", included: true },
              { text: "Up to 10 team members", included: true },
              { text: "API access", included: true },
              { text: "Analytics dashboard", included: true },
              { text: "Brand kit", included: true },
              { text: "2 months free!", included: true, highlight: true }
            ]
          }
        }
      ];

      // Create plans
      for (const planData of defaultPlans) {
        const plan = new Plan(planData);
        await plan.save();
        logger.info(`Created default plan: ${plan.name}`);
      }

      logger.info("Default plans initialized successfully");
    } catch (error) {
      logger.error("Error initializing default plans:", error);
      throw error;
    }
  }

  /**
   * Get plans for pricing page (excludes Enterprise as it's UI-only)
   */
  static async getPricingPlans() {
    try {
      const plans = await Plan.getVisiblePlans();
      
      // Add Enterprise plan as UI-only option
      const enterprisePlan = {
        name: "Enterprise",
        description: "Custom solutions for large organizations",
        price: null, // Custom pricing
        currency: "USD",
        duration: "custom",
        planType: PLAN_TYPES.ENTERPRISE,
        tier: PLAN_TIERS.ENTERPRISE,
        isActive: true,
        isVisible: true,
        isFeatured: false,
        sortOrder: 6,
        features: [
          { name: "Everything in Startup", description: "All Startup plan features", included: true },
          { name: "Unlimited Everything", description: "No limits on usage", included: true },
          { name: "Dedicated Support", description: "Dedicated account manager", included: true },
          { name: "Custom Integrations", description: "Custom API integrations", included: true },
          { name: "SLA Guarantee", description: "99.9% uptime guarantee", included: true },
          { name: "White-label Solution", description: "Fully branded solution", included: true }
        ],
        limits: {
          downloads: -1,
          projects: -1,
          storage: -1,
          apiCalls: -1,
          teamMembers: -1
        },
        benefits: {
          btn: { text: "Contact Sales", variant: "outline" },
          features: [
            { text: "Unlimited downloads", included: true },
            { text: "Unlimited projects", included: true },
            { text: "Unlimited storage", included: true },
            { text: "Unlimited team members", included: true },
            { text: "Dedicated support", included: true },
            { text: "Custom integrations", included: true },
            { text: "SLA guarantee", included: true },
            { text: "White-label solution", included: true }
          ]
        },
        displayName: "Enterprise",
        isUIOnly: true // Flag to indicate this is not stored in DB
      };

      return [...plans, enterprisePlan];
    } catch (error) {
      logger.error("Error getting pricing plans:", error);
      throw error;
    }
  }

  /**
   * Get plan recommendations based on user usage
   */
  static async getRecommendedPlan(user) {
    try {
      // Analyze user's current usage patterns
      const usage = await this.analyzeUserUsage(user);
      
      // Get available plans
      const plans = await Plan.getVisiblePlans();
      
      // Find the best fit plan
      let recommendedPlan = plans.find(plan => plan.planType === PLAN_TYPES.FREE);
      
      for (const plan of plans) {
        if (this.planMeetsUsage(plan, usage)) {
          recommendedPlan = plan;
          break;
        }
      }

      return {
        recommendedPlan,
        usage,
        reasons: this.getRecommendationReasons(recommendedPlan, usage)
      };
    } catch (error) {
      logger.error("Error getting recommended plan:", error);
      throw error;
    }
  }

  /**
   * Validate plan upgrade/downgrade
   */
  static async validatePlanChange(user, newPlan) {
    try {
      const currentPlan = user.currentPlan;
      const usage = await this.analyzeUserUsage(user);

      const validation = {
        valid: true,
        warnings: [],
        restrictions: []
      };

      // Check if downgrading would exceed limits
      if (currentPlan && this.isPlanDowngrade(currentPlan, newPlan)) {
        if (usage.downloads > newPlan.limits.downloads && newPlan.limits.downloads !== -1) {
          validation.warnings.push(`Your current usage (${usage.downloads} downloads) exceeds the new plan limit (${newPlan.limits.downloads})`);
        }
        
        if (usage.projects > newPlan.limits.projects && newPlan.limits.projects !== -1) {
          validation.warnings.push(`You have ${usage.projects} active projects, but the new plan allows only ${newPlan.limits.projects}`);
        }
        
        if (usage.storage > newPlan.limits.storage && newPlan.limits.storage !== -1) {
          validation.warnings.push(`Your storage usage (${usage.storage}MB) exceeds the new plan limit (${newPlan.limits.storage}MB)`);
        }
      }

      return validation;
    } catch (error) {
      logger.error("Error validating plan change:", error);
      throw error;
    }
  }

  // Helper methods
  static async analyzeUserUsage(user) {
    // This would integrate with actual usage tracking
    // For now, return mock data
    return {
      downloads: 0,
      projects: 0,
      storage: 0,
      apiCalls: 0,
      teamMembers: 1
    };
  }

  static planMeetsUsage(plan, usage) {
    return (
      (plan.limits.downloads === -1 || usage.downloads <= plan.limits.downloads) &&
      (plan.limits.projects === -1 || usage.projects <= plan.limits.projects) &&
      (plan.limits.storage === -1 || usage.storage <= plan.limits.storage) &&
      (plan.limits.apiCalls === -1 || usage.apiCalls <= plan.limits.apiCalls) &&
      (plan.limits.teamMembers === -1 || usage.teamMembers <= plan.limits.teamMembers)
    );
  }

  static getRecommendationReasons(plan, usage) {
    const reasons = [];
    
    if (plan.planType === PLAN_TYPES.FREE) {
      reasons.push("Perfect for getting started with basic features");
    } else if (plan.planType === PLAN_TYPES.INDIVIDUAL) {
      reasons.push("Great for individual creators and freelancers");
    } else if (plan.planType === PLAN_TYPES.STARTUP) {
      reasons.push("Ideal for growing teams and businesses");
    }

    return reasons;
  }

  static isPlanDowngrade(currentPlan, newPlan) {
    const tierOrder = {
      [PLAN_TIERS.FREE]: 0,
      [PLAN_TIERS.BASIC]: 1,
      [PLAN_TIERS.PRO]: 2,
      [PLAN_TIERS.ENTERPRISE]: 3
    };

    return tierOrder[newPlan.tier] < tierOrder[currentPlan.tier];
  }
}
