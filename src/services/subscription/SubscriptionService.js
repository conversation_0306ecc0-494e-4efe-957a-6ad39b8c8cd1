import User from "../../models/user.model.js";
import Plan from "../../models/plan.model.js";
import Transaction from "../../models/transaction.model.js";
import { TRANSACTION_TYPES, TRANSACTION_STATUSES } from "../../utils/constants.js";
import { SUBSCRIPTION_STATUS, PAYMENT_STATUS } from "../webhooks/WebhookHandler.js";

/**
 * Subscription Service
 * Handles all subscription-related business logic independent of payment gateways
 */
export class SubscriptionService {
  /**
   * Create or update user subscription based on webhook data
   * @param {Object} webhookData - Standardized webhook data
   * @returns {Object} - Result of subscription operation
   */
  async handleSubscriptionWebhook(webhookData) {
    try {
      const { eventType, data, gateway } = webhookData;
      const { subscription, customer, payment } = data;

      // Find or create user
      const user = await this.findOrCreateUser(customer, gateway);
      if (!user) {
        throw new Error('Failed to find or create user');
      }

      // Find plan
      const plan = await this.findPlan(subscription.planId, subscription.planName);
      if (!plan) {
        throw new Error(`Plan not found: ${subscription.planId || subscription.planName}`);
      }

      // Create transaction record
      const transaction = await this.createTransactionFromWebhook(
        user,
        plan,
        payment,
        subscription,
        gateway,
        eventType
      );

      // Handle different event types
      let result;
      switch (eventType) {
        case 'subscription.created':
        case 'subscription.activated':
          result = await this.activateSubscription(user, plan, transaction, subscription);
          break;
        
        case 'subscription.cancelled':
          result = await this.cancelSubscription(user, subscription.reason || 'User cancelled');
          break;
        
        case 'subscription.expired':
          result = await this.expireSubscription(user);
          break;
        
        case 'subscription.updated':
          result = await this.updateSubscription(user, plan, transaction, subscription);
          break;
        
        case 'payment.success':
          result = await this.handleSuccessfulPayment(user, plan, transaction, subscription);
          break;
        
        case 'payment.failed':
          result = await this.handleFailedPayment(user, transaction, subscription);
          break;
        
        default:
          result = { success: true, message: `Event ${eventType} processed but no action taken` };
      }

      return {
        success: true,
        user: user._id,
        transaction: transaction._id,
        action: eventType,
        result,
      };

    } catch (error) {
      console.error('Subscription webhook handling error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Find or create user based on customer data
   * @param {Object} customerData - Customer data from webhook
   * @param {string} gateway - Payment gateway name
   * @returns {Object} - User object
   */
  async findOrCreateUser(customerData, gateway) {
    const { email, externalId, name, phone } = customerData;

    // Try to find user by email first
    let user = await User.findOne({ email });

    if (!user) {
      // Create new user
      const [firstName, ...lastNameParts] = (name || '').split(' ');
      user = new User({
        email,
        username: email.split('@')[0] + '_' + Date.now(),
        firstName: firstName || '',
        lastName: lastNameParts.join(' ') || '',
        phoneNumber: phone,
        provider: 'email',
        createdBy: 'webhook',
        subscriptionMetadata: {
          source: gateway,
          [`${gateway}CustomerId`]: externalId,
        },
      });
      await user.save();
    } else {
      // Update existing user with gateway customer ID
      if (!user.subscriptionMetadata) {
        user.subscriptionMetadata = {};
      }
      user.subscriptionMetadata[`${gateway}CustomerId`] = externalId;
      await user.save();
    }

    return user;
  }

  /**
   * Find plan by ID or name
   * @param {string} planId - External plan ID
   * @param {string} planName - Plan name
   * @returns {Object} - Plan object
   */
  async findPlan(planId, planName) {
    // Try to find by external plan ID in metadata or by name
    let plan = await Plan.findOne({
      $or: [
        { name: planName },
        { 'metadata.externalId': planId },
      ],
    });

    if (!plan && planName) {
      // Try case-insensitive search
      plan = await Plan.findOne({
        name: { $regex: new RegExp(planName, 'i') },
      });
    }

    return plan;
  }

  /**
   * Create transaction record from webhook data
   * @param {Object} user - User object
   * @param {Object} plan - Plan object
   * @param {Object} paymentData - Payment data from webhook
   * @param {Object} subscriptionData - Subscription data from webhook
   * @param {string} gateway - Payment gateway name
   * @param {string} eventType - Event type
   * @returns {Object} - Transaction object
   */
  async createTransactionFromWebhook(user, plan, paymentData, subscriptionData, gateway, eventType) {
    const transactionData = {
      user: user._id,
      plan: plan._id,
      type: this.mapEventToTransactionType(eventType),
      amount: paymentData.amount || plan.price,
      originalAmount: paymentData.originalAmount || plan.price,
      discountAmount: paymentData.discountAmount || 0,
      currency: paymentData.currency || plan.currency || 'USD',
      status: this.mapPaymentStatusToTransactionStatus(paymentData.status),
      paymentMethod: paymentData.method || 'unknown',
      paymentGateway: gateway,
      gatewayTransactionId: paymentData.transactionId,
      gatewayResponse: paymentData.raw,
      userSubscriptionData: {
        previousTier: user.subscriptionTier,
        newTier: plan.duration,
        previousEndDate: user.subscriptionEndDate,
        newEndDate: subscriptionData.endDate,
        isUpgrade: this.isUpgrade(user.subscriptionTier, plan.duration),
        isRenewal: eventType.includes('payment') && user.isPro,
      },
      metadata: {
        source: gateway,
        webhookEvent: eventType,
        subscriptionId: subscriptionData.id,
        customData: {
          gatewaySubscriptionId: subscriptionData.id,
          gatewayCustomerId: subscriptionData.customerId,
          webhookId: paymentData.webhookId,
        },
      },
    };

    return await Transaction.create(transactionData);
  }

  /**
   * Activate user subscription
   * @param {Object} user - User object
   * @param {Object} plan - Plan object
   * @param {Object} transaction - Transaction object
   * @param {Object} subscriptionData - Subscription data
   * @returns {Object} - Result
   */
  async activateSubscription(user, plan, transaction, subscriptionData) {
    const startDate = subscriptionData.startDate ? new Date(subscriptionData.startDate) : new Date();
    const endDate = subscriptionData.endDate ? new Date(subscriptionData.endDate) : null;

    await user.activateSubscription(plan, transaction, startDate);
    
    if (endDate) {
      user.subscriptionEndDate = endDate;
      await user.save();
    }

    return {
      success: true,
      message: 'Subscription activated successfully',
      subscriptionTier: user.subscriptionTier,
      endDate: user.subscriptionEndDate,
    };
  }

  /**
   * Cancel user subscription
   * @param {Object} user - User object
   * @param {string} reason - Cancellation reason
   * @returns {Object} - Result
   */
  async cancelSubscription(user, reason) {
    await user.cancelSubscription(null, reason);
    
    return {
      success: true,
      message: 'Subscription cancelled successfully',
      effectiveDate: user.cancellation?.effectiveDate,
    };
  }

  /**
   * Expire user subscription
   * @param {Object} user - User object
   * @returns {Object} - Result
   */
  async expireSubscription(user) {
    user.subscriptionStatus = 'expired';
    user.isPro = false;
    user.autoRenew = false;
    await user.save();

    return {
      success: true,
      message: 'Subscription expired',
    };
  }

  /**
   * Update user subscription
   * @param {Object} user - User object
   * @param {Object} plan - Plan object
   * @param {Object} transaction - Transaction object
   * @param {Object} subscriptionData - Subscription data
   * @returns {Object} - Result
   */
  async updateSubscription(user, plan, transaction, subscriptionData) {
    await user.changeSubscription(plan, transaction);
    
    if (subscriptionData.endDate) {
      user.subscriptionEndDate = new Date(subscriptionData.endDate);
      await user.save();
    }

    return {
      success: true,
      message: 'Subscription updated successfully',
      newTier: user.subscriptionTier,
    };
  }

  /**
   * Handle successful payment
   * @param {Object} user - User object
   * @param {Object} plan - Plan object
   * @param {Object} transaction - Transaction object
   * @param {Object} subscriptionData - Subscription data
   * @returns {Object} - Result
   */
  async handleSuccessfulPayment(user, plan, transaction, subscriptionData) {
    // Update transaction status
    transaction.status = TRANSACTION_STATUSES.COMPLETED;
    transaction.processedAt = new Date();
    await transaction.save();

    // If this is a renewal payment
    if (user.isPro && subscriptionData.endDate) {
      await user.renewSubscription(plan, transaction);
      return {
        success: true,
        message: 'Subscription renewed successfully',
        newEndDate: user.subscriptionEndDate,
      };
    }

    // If this is a new subscription
    if (!user.isPro) {
      return await this.activateSubscription(user, plan, transaction, subscriptionData);
    }

    return {
      success: true,
      message: 'Payment processed successfully',
    };
  }

  /**
   * Handle failed payment
   * @param {Object} user - User object
   * @param {Object} transaction - Transaction object
   * @param {Object} subscriptionData - Subscription data
   * @returns {Object} - Result
   */
  async handleFailedPayment(user, transaction, subscriptionData) {
    // Update transaction status
    transaction.status = TRANSACTION_STATUSES.FAILED;
    transaction.failedAt = new Date();
    await transaction.save();

    // You might want to implement retry logic or grace period here
    
    return {
      success: true,
      message: 'Payment failure recorded',
      action: 'payment_failed',
    };
  }

  /**
   * Map webhook event to transaction type
   * @param {string} eventType - Webhook event type
   * @returns {string} - Transaction type
   */
  mapEventToTransactionType(eventType) {
    const eventMap = {
      'subscription.created': TRANSACTION_TYPES.SUBSCRIPTION_PURCHASE,
      'subscription.activated': TRANSACTION_TYPES.SUBSCRIPTION_PURCHASE,
      'subscription.updated': TRANSACTION_TYPES.SUBSCRIPTION_UPGRADE,
      'payment.success': TRANSACTION_TYPES.SUBSCRIPTION_RENEWAL,
    };

    return eventMap[eventType] || TRANSACTION_TYPES.SUBSCRIPTION_PURCHASE;
  }

  /**
   * Map payment status to transaction status
   * @param {string} paymentStatus - Payment status from webhook
   * @returns {string} - Transaction status
   */
  mapPaymentStatusToTransactionStatus(paymentStatus) {
    const statusMap = {
      [PAYMENT_STATUS.SUCCESS]: TRANSACTION_STATUSES.COMPLETED,
      [PAYMENT_STATUS.FAILED]: TRANSACTION_STATUSES.FAILED,
      [PAYMENT_STATUS.PENDING]: TRANSACTION_STATUSES.PENDING,
      [PAYMENT_STATUS.REFUNDED]: TRANSACTION_STATUSES.REFUNDED,
    };

    return statusMap[paymentStatus] || TRANSACTION_STATUSES.PENDING;
  }

  /**
   * Check if plan change is an upgrade
   * @param {string} currentTier - Current subscription tier
   * @param {string} newTier - New subscription tier
   * @returns {boolean} - Whether it's an upgrade
   */
  isUpgrade(currentTier, newTier) {
    const tierHierarchy = {
      'free': 0,
      'monthly': 1,
      'yearly': 2,
      'lifetime': 3,
    };

    return (tierHierarchy[newTier] || 0) > (tierHierarchy[currentTier] || 0);
  }
}

// Create singleton instance
export const subscriptionService = new SubscriptionService();
