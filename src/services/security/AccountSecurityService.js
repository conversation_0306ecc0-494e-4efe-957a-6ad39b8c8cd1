import UserSession, { SESSION_STATUS, DEVICE_TYPES } from "../../models/userSession.model.js";
import User from "../../models/user.model.js";
import { sessionService } from "../../config/redis.js";
import logger from "../../utils/logger.js";
import crypto from "crypto";
import UAParser from "ua-parser-js";

/**
 * Account Security Service
 * Handles session management, account sharing prevention, and security monitoring
 */
export class AccountSecurityService {
  
  // Configuration constants
  static MAX_CONCURRENT_SESSIONS = 3;
  static SUSPICIOUS_LOGIN_THRESHOLD = 5; // Different IPs in short time
  static LOCATION_CHANGE_THRESHOLD = 500; // km
  static SESSION_INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  
  /**
   * Create a new user session
   * @param {Object} user - User object
   * @param {Object} sessionData - Session creation data
   * @returns {Object} - Session creation result
   */
  static async createSession(user, sessionData) {
    try {
      const {
        jwtTokenId,
        ipAddress,
        userAgent,
        expiresAt,
        locationData = {}
      } = sessionData;

      // Parse user agent for device info
      const deviceInfo = this.parseUserAgent(userAgent);
      
      // Generate unique session ID
      const sessionId = this.generateSessionId();
      
      // Check for existing sessions and potential security issues
      const securityCheck = await this.performSecurityCheck(user._id, ipAddress, deviceInfo);
      
      // Create session record
      const session = new UserSession({
        user: user._id,
        sessionId,
        jwtTokenId,
        deviceInfo,
        networkInfo: {
          ipAddress,
          ...locationData
        },
        expiresAt,
        securityFlags: securityCheck.flags,
        metadata: {
          createdBy: "login",
          securityScore: securityCheck.score
        }
      });

      await session.save();

      // Handle multiple sessions if detected
      if (securityCheck.flags.multipleSimultaneousLogins) {
        await this.handleMultipleSessions(user._id, session._id);
      }

      // Store session in Redis for quick access
      await sessionService.setSession(sessionId, {
        userId: user._id.toString(),
        sessionId: session._id.toString(),
        deviceFingerprint: deviceInfo.deviceFingerprint,
        ipAddress,
        lastActivity: new Date().toISOString()
      }, Math.floor((expiresAt - new Date()) / 1000));

      logger.info(`Session created for user ${user._id}`, {
        sessionId,
        ipAddress,
        deviceType: deviceInfo.deviceType,
        securityFlags: securityCheck.flags
      });

      return {
        success: true,
        session,
        securityWarnings: securityCheck.warnings
      };

    } catch (error) {
      logger.error("Error creating session:", error);
      throw error;
    }
  }

  /**
   * Validate and update existing session
   * @param {string} sessionId - Session ID
   * @param {string} endpoint - Current endpoint being accessed
   * @returns {Object} - Validation result
   */
  static async validateSession(sessionId, endpoint = null) {
    try {
      // Check Redis first for performance
      const redisSession = await sessionService.getSession(sessionId);
      if (!redisSession) {
        return { valid: false, reason: "session_not_found" };
      }

      // Get full session from database
      const session = await UserSession.findById(redisSession.sessionId)
        .populate("user", "isDeleted isSuspended");

      if (!session) {
        await sessionService.deleteSession(sessionId);
        return { valid: false, reason: "session_not_found" };
      }

      // Check if session is still valid
      if (!session.isActive) {
        await sessionService.deleteSession(sessionId);
        return { valid: false, reason: "session_inactive" };
      }

      // Check if user account is suspended or deleted
      if (session.user.isDeleted || session.user.isSuspended) {
        await session.terminate("account_suspended");
        await sessionService.deleteSession(sessionId);
        return { valid: false, reason: "account_suspended" };
      }

      // Update activity
      await session.updateActivity(endpoint);
      
      // Update Redis session
      await sessionService.setSession(sessionId, {
        ...redisSession,
        lastActivity: new Date().toISOString()
      });

      return {
        valid: true,
        session,
        user: session.user
      };

    } catch (error) {
      logger.error("Error validating session:", error);
      return { valid: false, reason: "validation_error" };
    }
  }

  /**
   * Terminate a specific session
   * @param {string} sessionId - Session ID to terminate
   * @param {string} reason - Termination reason
   * @returns {boolean} - Success status
   */
  static async terminateSession(sessionId, reason = "user_logout") {
    try {
      const session = await UserSession.findOne({ sessionId });
      if (session) {
        await session.terminate(reason);
      }
      
      await sessionService.deleteSession(sessionId);
      
      logger.info(`Session terminated: ${sessionId}`, { reason });
      return true;
    } catch (error) {
      logger.error("Error terminating session:", error);
      return false;
    }
  }

  /**
   * Terminate all sessions for a user
   * @param {string} userId - User ID
   * @param {string} reason - Termination reason
   * @returns {number} - Number of sessions terminated
   */
  static async terminateAllUserSessions(userId, reason = "admin_terminated") {
    try {
      const sessions = await UserSession.getActiveSessions(userId);
      
      // Terminate in database
      await UserSession.terminateAllUserSessions(userId, reason);
      
      // Remove from Redis
      for (const session of sessions) {
        await sessionService.deleteSession(session.sessionId);
      }

      logger.info(`All sessions terminated for user ${userId}`, { 
        count: sessions.length, 
        reason 
      });

      return sessions.length;
    } catch (error) {
      logger.error("Error terminating all user sessions:", error);
      throw error;
    }
  }

  /**
   * Detect and handle account sharing
   * @param {string} userId - User ID
   * @returns {Object} - Detection result
   */
  static async detectAccountSharing(userId) {
    try {
      const activeSessions = await UserSession.getActiveSessions(userId);
      
      if (activeSessions.length <= 1) {
        return { sharing: false, sessions: activeSessions };
      }

      // Analyze sessions for sharing indicators
      const indicators = {
        multipleIPs: new Set(activeSessions.map(s => s.networkInfo.ipAddress)).size > 1,
        multipleLocations: this.hasMultipleLocations(activeSessions),
        simultaneousActivity: this.hasSimultaneousActivity(activeSessions),
        differentDevices: this.hasDifferentDevices(activeSessions)
      };

      const sharingScore = this.calculateSharingScore(indicators, activeSessions);
      const isSharing = sharingScore > 0.7; // 70% confidence threshold

      if (isSharing) {
        // Mark sessions as suspicious
        for (const session of activeSessions) {
          await session.markSuspicious("potential_account_sharing");
        }

        logger.warn(`Account sharing detected for user ${userId}`, {
          sessionCount: activeSessions.length,
          indicators,
          sharingScore
        });
      }

      return {
        sharing: isSharing,
        confidence: sharingScore,
        indicators,
        sessions: activeSessions
      };

    } catch (error) {
      logger.error("Error detecting account sharing:", error);
      throw error;
    }
  }

  /**
   * Get user session summary
   * @param {string} userId - User ID
   * @returns {Object} - Session summary
   */
  static async getUserSessionSummary(userId) {
    try {
      const activeSessions = await UserSession.getActiveSessions(userId);
      const recentSessions = await UserSession.find({
        user: userId,
        loginTime: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
      }).sort({ loginTime: -1 }).limit(10);

      return {
        activeSessionCount: activeSessions.length,
        activeSessions: activeSessions.map(s => ({
          sessionId: s.sessionId,
          deviceType: s.deviceInfo.deviceType,
          browser: s.deviceInfo.browser,
          ipAddress: s.networkInfo.ipAddress,
          location: `${s.networkInfo.city || 'Unknown'}, ${s.networkInfo.country || 'Unknown'}`,
          loginTime: s.loginTime,
          lastActivity: s.lastActivity,
          isSuspicious: s.status === SESSION_STATUS.SUSPICIOUS
        })),
        recentSessions: recentSessions.map(s => ({
          deviceType: s.deviceInfo.deviceType,
          ipAddress: s.networkInfo.ipAddress,
          loginTime: s.loginTime,
          status: s.status,
          duration: s.duration
        }))
      };
    } catch (error) {
      logger.error("Error getting user session summary:", error);
      throw error;
    }
  }

  // Helper methods
  static parseUserAgent(userAgent) {
    const parser = new UAParser(userAgent);
    const result = parser.getResult();
    
    return {
      userAgent,
      deviceType: this.determineDeviceType(result),
      browser: {
        name: result.browser.name,
        version: result.browser.version
      },
      os: {
        name: result.os.name,
        version: result.os.version
      },
      deviceFingerprint: this.generateDeviceFingerprint(result, userAgent)
    };
  }

  static determineDeviceType(parsedUA) {
    if (parsedUA.device.type === 'mobile') return DEVICE_TYPES.MOBILE;
    if (parsedUA.device.type === 'tablet') return DEVICE_TYPES.TABLET;
    if (parsedUA.device.type === undefined && parsedUA.os.name) return DEVICE_TYPES.DESKTOP;
    return DEVICE_TYPES.UNKNOWN;
  }

  static generateDeviceFingerprint(parsedUA, userAgent) {
    const fingerprint = `${parsedUA.browser.name}-${parsedUA.os.name}-${parsedUA.device.model || 'unknown'}`;
    return crypto.createHash('sha256').update(fingerprint + userAgent).digest('hex').substring(0, 16);
  }

  static generateSessionId() {
    return crypto.randomBytes(32).toString('hex');
  }

  static async performSecurityCheck(userId, ipAddress, deviceInfo) {
    const flags = {
      isFirstTimeDevice: false,
      isNewLocation: false,
      isSuspiciousActivity: false,
      requiresVerification: false,
      multipleSimultaneousLogins: false
    };

    const warnings = [];
    let score = 0;

    // Check for multiple sessions
    const activeSessionCount = await UserSession.countDocuments({
      user: userId,
      status: SESSION_STATUS.ACTIVE,
      expiresAt: { $gt: new Date() }
    });

    if (activeSessionCount >= this.MAX_CONCURRENT_SESSIONS) {
      flags.multipleSimultaneousLogins = true;
      warnings.push("Multiple simultaneous sessions detected");
      score += 0.4;
    }

    // Check for new device
    const existingDevice = await UserSession.findOne({
      user: userId,
      "deviceInfo.deviceFingerprint": deviceInfo.deviceFingerprint
    });

    if (!existingDevice) {
      flags.isFirstTimeDevice = true;
      warnings.push("New device detected");
      score += 0.2;
    }

    // Check for suspicious IP activity
    const recentIPSessions = await UserSession.getSessionsByIP(ipAddress, 1);
    if (recentIPSessions.length > this.SUSPICIOUS_LOGIN_THRESHOLD) {
      flags.isSuspiciousActivity = true;
      warnings.push("Suspicious IP activity detected");
      score += 0.3;
    }

    return { flags, warnings, score };
  }

  static async handleMultipleSessions(userId, currentSessionId) {
    const activeSessions = await UserSession.getActiveSessions(userId);
    
    // If over limit, terminate oldest sessions
    if (activeSessions.length > this.MAX_CONCURRENT_SESSIONS) {
      const sessionsToTerminate = activeSessions
        .filter(s => s._id.toString() !== currentSessionId.toString())
        .sort((a, b) => a.lastActivity - b.lastActivity)
        .slice(0, activeSessions.length - this.MAX_CONCURRENT_SESSIONS);

      for (const session of sessionsToTerminate) {
        await session.terminate("multiple_sessions_detected");
        await sessionService.deleteSession(session.sessionId);
      }

      logger.info(`Terminated ${sessionsToTerminate.length} sessions due to limit exceeded`, {
        userId,
        currentSessionId
      });
    }
  }

  static hasMultipleLocations(sessions) {
    const locations = sessions
      .filter(s => s.networkInfo.city && s.networkInfo.country)
      .map(s => `${s.networkInfo.city},${s.networkInfo.country}`);
    return new Set(locations).size > 1;
  }

  static hasSimultaneousActivity(sessions) {
    const now = new Date();
    const recentActivity = sessions.filter(s => 
      (now - s.lastActivity) < this.SESSION_INACTIVITY_TIMEOUT
    );
    return recentActivity.length > 1;
  }

  static hasDifferentDevices(sessions) {
    const deviceTypes = new Set(sessions.map(s => s.deviceInfo.deviceType));
    return deviceTypes.size > 1;
  }

  static calculateSharingScore(indicators, sessions) {
    let score = 0;
    
    if (indicators.multipleIPs) score += 0.3;
    if (indicators.multipleLocations) score += 0.4;
    if (indicators.simultaneousActivity) score += 0.2;
    if (indicators.differentDevices) score += 0.1;
    
    // Increase score based on session count
    if (sessions.length > 3) score += 0.2;
    if (sessions.length > 5) score += 0.3;
    
    return Math.min(score, 1.0);
  }
}
