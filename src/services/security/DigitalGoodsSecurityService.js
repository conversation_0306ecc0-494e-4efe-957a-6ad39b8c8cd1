import User from "../../models/user.model.js";
import { sessionService } from "../../config/redis.js";
import logger from "../../utils/logger.js";
import crypto from "crypto";

/**
 * Digital Goods Security Service
 * Implements security measures specific to digital goods platforms
 * to protect against unauthorized access and revenue loss
 */
export class DigitalGoodsSecurityService {
  
  // Configuration constants
  static DOWNLOAD_RATE_LIMIT = 50; // Downloads per hour
  static SUSPICIOUS_DOWNLOAD_THRESHOLD = 20; // Downloads in 10 minutes
  static IP_DOWNLOAD_LIMIT = 100; // Downloads per IP per day
  static CONCURRENT_DOWNLOAD_LIMIT = 3; // Simultaneous downloads
  
  /**
   * Track and validate digital goods access
   * @param {Object} user - User object
   * @param {Object} product - Product being accessed
   * @param {Object} request - Request object with IP, user agent, etc.
   * @returns {Object} - Access validation result
   */
  static async validateDigitalGoodsAccess(user, product, request) {
    try {
      const validation = {
        allowed: true,
        warnings: [],
        restrictions: [],
        securityFlags: []
      };

      // Check user subscription status
      if (product.isPaid && !user.hasActiveSubscription()) {
        validation.allowed = false;
        validation.restrictions.push("Active subscription required");
        return validation;
      }

      // Check account suspension
      if (user.isCurrentlySuspended()) {
        validation.allowed = false;
        validation.restrictions.push("Account is suspended");
        return validation;
      }

      // Check for account sharing flags
      if (user.securityFlags.accountSharingDetected) {
        validation.allowed = false;
        validation.restrictions.push("Access restricted due to account sharing violation");
        return validation;
      }

      // Rate limiting checks
      const rateLimitCheck = await this.checkDownloadRateLimit(user._id, request.ip);
      if (!rateLimitCheck.allowed) {
        validation.allowed = false;
        validation.restrictions.push("Download rate limit exceeded");
        return validation;
      }

      // Concurrent download check
      const concurrentCheck = await this.checkConcurrentDownloads(user._id);
      if (!concurrentCheck.allowed) {
        validation.allowed = false;
        validation.restrictions.push("Too many concurrent downloads");
        return validation;
      }

      // Suspicious activity detection
      const suspiciousCheck = await this.detectSuspiciousDownloadActivity(user._id, request.ip);
      if (suspiciousCheck.suspicious) {
        validation.warnings.push("Suspicious download pattern detected");
        validation.securityFlags.push("suspicious_download_pattern");
        
        // Flag user account
        await user.flagSuspiciousActivity();
      }

      // IP-based download limit
      const ipLimitCheck = await this.checkIPDownloadLimit(request.ip);
      if (!ipLimitCheck.allowed) {
        validation.allowed = false;
        validation.restrictions.push("IP download limit exceeded");
        return validation;
      }

      return validation;
    } catch (error) {
      logger.error("Error validating digital goods access:", error);
      return {
        allowed: false,
        restrictions: ["Security validation failed"],
        warnings: [],
        securityFlags: []
      };
    }
  }

  /**
   * Generate secure download token
   * @param {Object} user - User object
   * @param {Object} product - Product object
   * @param {Object} request - Request object
   * @returns {Object} - Secure download token and metadata
   */
  static async generateSecureDownloadToken(user, product, request) {
    try {
      const tokenData = {
        userId: user._id.toString(),
        productId: product._id.toString(),
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'],
        timestamp: Date.now(),
        expiresAt: Date.now() + (15 * 60 * 1000), // 15 minutes
        sessionId: request.headers['x-session-id'] || null
      };

      // Create secure token
      const token = crypto.randomBytes(32).toString('hex');
      const signature = this.createTokenSignature(token, tokenData);

      // Store token in Redis with expiration
      await sessionService.setTempData(`download_token:${token}`, {
        ...tokenData,
        signature
      }, 900); // 15 minutes

      // Log download initiation
      await this.logDownloadAccess(user, product, request, 'token_generated');

      return {
        token,
        expiresAt: tokenData.expiresAt,
        restrictions: {
          ipAddress: tokenData.ipAddress,
          userAgent: tokenData.userAgent,
          singleUse: true
        }
      };
    } catch (error) {
      logger.error("Error generating secure download token:", error);
      throw error;
    }
  }

  /**
   * Validate and consume download token
   * @param {string} token - Download token
   * @param {Object} request - Request object
   * @returns {Object} - Token validation result
   */
  static async validateDownloadToken(token, request) {
    try {
      // Get token data from Redis
      const tokenData = await sessionService.getTempData(`download_token:${token}`);
      
      if (!tokenData) {
        return { valid: false, reason: "Token not found or expired" };
      }

      // Verify token signature
      const expectedSignature = this.createTokenSignature(token, tokenData);
      if (tokenData.signature !== expectedSignature) {
        return { valid: false, reason: "Invalid token signature" };
      }

      // Check expiration
      if (Date.now() > tokenData.expiresAt) {
        await sessionService.deleteTempData(`download_token:${token}`);
        return { valid: false, reason: "Token expired" };
      }

      // Validate request constraints
      if (tokenData.ipAddress !== request.ip) {
        return { valid: false, reason: "IP address mismatch" };
      }

      if (tokenData.userAgent !== request.headers['user-agent']) {
        return { valid: false, reason: "User agent mismatch" };
      }

      // Mark token as used (delete from Redis)
      await sessionService.deleteTempData(`download_token:${token}`);

      return {
        valid: true,
        tokenData: {
          userId: tokenData.userId,
          productId: tokenData.productId,
          timestamp: tokenData.timestamp
        }
      };
    } catch (error) {
      logger.error("Error validating download token:", error);
      return { valid: false, reason: "Token validation failed" };
    }
  }

  /**
   * Log download access for audit and analytics
   * @param {Object} user - User object
   * @param {Object} product - Product object
   * @param {Object} request - Request object
   * @param {string} action - Action type
   */
  static async logDownloadAccess(user, product, request, action = 'download') {
    try {
      const logEntry = {
        userId: user._id,
        productId: product._id,
        action,
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'],
        timestamp: new Date(),
        sessionId: request.headers['x-session-id'],
        metadata: {
          productName: product.name,
          productCategory: product.category,
          userPlan: user.currentPlan?.name || 'Free',
          downloadSize: product.fileSize || 0
        }
      };

      // Store in Redis for real-time analytics
      await sessionService.setTempData(
        `download_log:${user._id}:${Date.now()}`,
        logEntry,
        86400 // 24 hours
      );

      // Log for audit trail
      logger.info(`Digital goods access: ${action}`, logEntry);

      // Update user download count
      await this.updateUserDownloadStats(user._id, product);

    } catch (error) {
      logger.error("Error logging download access:", error);
    }
  }

  /**
   * Check download rate limiting
   * @param {string} userId - User ID
   * @param {string} ipAddress - IP address
   * @returns {Object} - Rate limit check result
   */
  static async checkDownloadRateLimit(userId, ipAddress) {
    try {
      // Check user-based rate limit
      const userKey = `download_rate:user:${userId}`;
      const userResult = await sessionService.checkRateLimit(
        userKey,
        this.DOWNLOAD_RATE_LIMIT,
        3600 // 1 hour
      );

      if (!userResult.allowed) {
        return { allowed: false, reason: "User download rate limit exceeded" };
      }

      // Check IP-based rate limit
      const ipKey = `download_rate:ip:${ipAddress}`;
      const ipResult = await sessionService.checkRateLimit(
        ipKey,
        this.IP_DOWNLOAD_LIMIT,
        86400 // 24 hours
      );

      if (!ipResult.allowed) {
        return { allowed: false, reason: "IP download rate limit exceeded" };
      }

      return { allowed: true };
    } catch (error) {
      logger.error("Error checking download rate limit:", error);
      return { allowed: true }; // Allow on error to avoid blocking legitimate users
    }
  }

  /**
   * Check concurrent downloads
   * @param {string} userId - User ID
   * @returns {Object} - Concurrent download check result
   */
  static async checkConcurrentDownloads(userId) {
    try {
      const key = `concurrent_downloads:${userId}`;
      const current = await sessionService.getCache(key) || 0;

      if (current >= this.CONCURRENT_DOWNLOAD_LIMIT) {
        return { allowed: false, current };
      }

      return { allowed: true, current };
    } catch (error) {
      logger.error("Error checking concurrent downloads:", error);
      return { allowed: true, current: 0 };
    }
  }

  /**
   * Track concurrent download start
   * @param {string} userId - User ID
   * @param {string} downloadId - Unique download ID
   */
  static async trackDownloadStart(userId, downloadId) {
    try {
      const key = `concurrent_downloads:${userId}`;
      const current = await sessionService.getCache(key) || 0;
      await sessionService.setCache(key, current + 1, 3600); // 1 hour expiry

      // Track individual download
      await sessionService.setCache(`download:${downloadId}`, userId, 3600);
    } catch (error) {
      logger.error("Error tracking download start:", error);
    }
  }

  /**
   * Track concurrent download end
   * @param {string} userId - User ID
   * @param {string} downloadId - Unique download ID
   */
  static async trackDownloadEnd(userId, downloadId) {
    try {
      const key = `concurrent_downloads:${userId}`;
      const current = await sessionService.getCache(key) || 0;
      if (current > 0) {
        await sessionService.setCache(key, current - 1, 3600);
      }

      // Remove individual download tracking
      await sessionService.deleteCache(`download:${downloadId}`);
    } catch (error) {
      logger.error("Error tracking download end:", error);
    }
  }

  /**
   * Detect suspicious download activity
   * @param {string} userId - User ID
   * @param {string} ipAddress - IP address
   * @returns {Object} - Suspicious activity detection result
   */
  static async detectSuspiciousDownloadActivity(userId, ipAddress) {
    try {
      // Check for rapid downloads (more than threshold in 10 minutes)
      const rapidKey = `rapid_downloads:${userId}`;
      const rapidResult = await sessionService.checkRateLimit(
        rapidKey,
        this.SUSPICIOUS_DOWNLOAD_THRESHOLD,
        600 // 10 minutes
      );

      if (!rapidResult.allowed) {
        return {
          suspicious: true,
          reason: "Rapid download pattern detected",
          severity: "high"
        };
      }

      // Check for downloads from multiple IPs in short time
      const ipKey = `user_ips:${userId}`;
      const recentIPs = await sessionService.getCache(ipKey) || [];
      
      if (!recentIPs.includes(ipAddress)) {
        recentIPs.push(ipAddress);
        if (recentIPs.length > 3) { // More than 3 IPs in recent activity
          return {
            suspicious: true,
            reason: "Multiple IP addresses detected",
            severity: "medium"
          };
        }
        await sessionService.setCache(ipKey, recentIPs.slice(-5), 3600); // Keep last 5 IPs
      }

      return { suspicious: false };
    } catch (error) {
      logger.error("Error detecting suspicious download activity:", error);
      return { suspicious: false };
    }
  }

  // Helper methods
  static createTokenSignature(token, tokenData) {
    const payload = `${token}:${tokenData.userId}:${tokenData.productId}:${tokenData.timestamp}`;
    return crypto.createHmac('sha256', process.env.JWT_SECRET || 'fallback-secret')
      .update(payload)
      .digest('hex');
  }

  static async checkIPDownloadLimit(ipAddress) {
    try {
      const key = `ip_downloads:${ipAddress}`;
      const result = await sessionService.checkRateLimit(
        key,
        this.IP_DOWNLOAD_LIMIT,
        86400 // 24 hours
      );
      return result;
    } catch (error) {
      logger.error("Error checking IP download limit:", error);
      return { allowed: true };
    }
  }

  static async updateUserDownloadStats(userId, product) {
    try {
      const key = `user_stats:${userId}`;
      const stats = await sessionService.getCache(key) || {
        totalDownloads: 0,
        monthlyDownloads: 0,
        lastDownload: null
      };

      stats.totalDownloads += 1;
      stats.monthlyDownloads += 1;
      stats.lastDownload = new Date().toISOString();

      await sessionService.setCache(key, stats, 86400 * 30); // 30 days
    } catch (error) {
      logger.error("Error updating user download stats:", error);
    }
  }
}
