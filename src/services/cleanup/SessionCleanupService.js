import UserSession, { SESSION_STATUS } from "../../models/userSession.model.js";
import logger from "../../utils/logger.js";
import cron from "node-cron";

/**
 * Session Cleanup Service
 * Handles automatic cleanup of expired sessions and maintenance tasks
 */
export class SessionCleanupService {
  
  static isCleanupRunning = false;
  static lastCleanupTime = null;
  static cleanupStats = {
    totalCleaned: 0,
    lastRunDuration: 0,
    errors: 0
  };

  /**
   * Initialize session cleanup service with cron jobs
   */
  static initialize() {
    try {
      // Run cleanup every hour
      cron.schedule('0 * * * *', async () => {
        await this.runCleanup();
      });

      // Run deep cleanup daily at 2 AM
      cron.schedule('0 2 * * *', async () => {
        await this.runDeepCleanup();
      });

      logger.info("Session cleanup service initialized");
    } catch (error) {
      logger.error("Failed to initialize session cleanup service:", error);
    }
  }

  /**
   * Run regular session cleanup
   */
  static async runCleanup() {
    if (this.isCleanupRunning) {
      logger.warn("Session cleanup already running, skipping...");
      return;
    }

    this.isCleanupRunning = true;
    const startTime = Date.now();

    try {
      logger.info("Starting session cleanup...");

      // Clean expired sessions
      const expiredCount = await this.cleanExpiredSessions();
      
      // Clean terminated sessions older than 30 days
      const terminatedCount = await this.cleanOldTerminatedSessions();
      
      // Update statistics
      const duration = Date.now() - startTime;
      this.cleanupStats.totalCleaned += expiredCount + terminatedCount;
      this.cleanupStats.lastRunDuration = duration;
      this.lastCleanupTime = new Date();

      logger.info(`Session cleanup completed: ${expiredCount} expired, ${terminatedCount} old terminated sessions cleaned in ${duration}ms`);

    } catch (error) {
      this.cleanupStats.errors++;
      logger.error("Session cleanup failed:", error);
    } finally {
      this.isCleanupRunning = false;
    }
  }

  /**
   * Run deep cleanup with additional maintenance tasks
   */
  static async runDeepCleanup() {
    try {
      logger.info("Starting deep session cleanup...");

      // Run regular cleanup first
      await this.runCleanup();

      // Additional deep cleanup tasks
      await this.cleanOrphanedSessions();
      await this.updateSessionStatistics();
      await this.cleanupRedisSessionData();

      logger.info("Deep session cleanup completed");

    } catch (error) {
      logger.error("Deep session cleanup failed:", error);
    }
  }

  /**
   * Clean expired sessions
   */
  static async cleanExpiredSessions() {
    try {
      const result = await UserSession.deleteMany({
        expiresAt: { $lt: new Date() },
        status: { $ne: SESSION_STATUS.TERMINATED }
      });

      logger.info(`Cleaned ${result.deletedCount} expired sessions`);
      return result.deletedCount;

    } catch (error) {
      logger.error("Failed to clean expired sessions:", error);
      return 0;
    }
  }

  /**
   * Clean old terminated sessions (older than 30 days)
   */
  static async cleanOldTerminatedSessions() {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const result = await UserSession.deleteMany({
        status: SESSION_STATUS.TERMINATED,
        logoutTime: { $lt: thirtyDaysAgo }
      });

      logger.info(`Cleaned ${result.deletedCount} old terminated sessions`);
      return result.deletedCount;

    } catch (error) {
      logger.error("Failed to clean old terminated sessions:", error);
      return 0;
    }
  }

  /**
   * Clean orphaned sessions (sessions without valid users)
   */
  static async cleanOrphanedSessions() {
    try {
      // Find sessions with invalid user references
      const orphanedSessions = await UserSession.aggregate([
        {
          $lookup: {
            from: "users",
            localField: "user",
            foreignField: "_id",
            as: "userDoc"
          }
        },
        {
          $match: {
            userDoc: { $size: 0 }
          }
        },
        {
          $project: { _id: 1 }
        }
      ]);

      if (orphanedSessions.length > 0) {
        const orphanedIds = orphanedSessions.map(session => session._id);
        const result = await UserSession.deleteMany({
          _id: { $in: orphanedIds }
        });

        logger.info(`Cleaned ${result.deletedCount} orphaned sessions`);
        return result.deletedCount;
      }

      return 0;

    } catch (error) {
      logger.error("Failed to clean orphaned sessions:", error);
      return 0;
    }
  }

  /**
   * Update session statistics
   */
  static async updateSessionStatistics() {
    try {
      const stats = await UserSession.aggregate([
        {
          $group: {
            _id: "$status",
            count: { $sum: 1 }
          }
        }
      ]);

      logger.info("Session statistics updated:", stats);

    } catch (error) {
      logger.error("Failed to update session statistics:", error);
    }
  }

  /**
   * Clean up Redis session data for deleted sessions
   */
  static async cleanupRedisSessionData() {
    try {
      const { sessionService } = await import("../../config/redis.js");
      
      // This would require getting all Redis keys and checking if corresponding
      // database sessions exist. For now, we'll rely on Redis TTL for cleanup.
      logger.info("Redis session cleanup completed (TTL-based)");

    } catch (error) {
      logger.error("Failed to cleanup Redis session data:", error);
    }
  }

  /**
   * Manual cleanup trigger (for admin use)
   */
  static async manualCleanup() {
    if (this.isCleanupRunning) {
      throw new Error("Cleanup is already running");
    }

    await this.runCleanup();
    return {
      success: true,
      stats: this.cleanupStats,
      lastCleanupTime: this.lastCleanupTime
    };
  }

  /**
   * Get cleanup statistics
   */
  static getCleanupStats() {
    return {
      ...this.cleanupStats,
      lastCleanupTime: this.lastCleanupTime,
      isRunning: this.isCleanupRunning
    };
  }

  /**
   * Clean sessions for a specific user (useful for account deletion)
   */
  static async cleanUserSessions(userId) {
    try {
      const result = await UserSession.deleteMany({ user: userId });
      logger.info(`Cleaned ${result.deletedCount} sessions for user ${userId}`);
      return result.deletedCount;

    } catch (error) {
      logger.error(`Failed to clean sessions for user ${userId}:`, error);
      return 0;
    }
  }

  /**
   * Clean sessions older than specified days
   */
  static async cleanSessionsOlderThan(days) {
    try {
      const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      
      const result = await UserSession.deleteMany({
        createdAt: { $lt: cutoffDate }
      });

      logger.info(`Cleaned ${result.deletedCount} sessions older than ${days} days`);
      return result.deletedCount;

    } catch (error) {
      logger.error(`Failed to clean sessions older than ${days} days:`, error);
      return 0;
    }
  }
}

// Auto-initialize if not in test environment
if (process.env.NODE_ENV !== 'test') {
  SessionCleanupService.initialize();
}
