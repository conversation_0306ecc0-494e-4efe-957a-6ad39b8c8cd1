import { Resend } from "resend";

// Initialize Resend lazily
let resend = null;

const getResendClient = () => {
  if (!resend) {
    if (!process.env.RESEND_API_KEY) {
      throw new Error("RESEND_API_KEY environment variable is not set");
    }
    resend = new Resend(process.env.RESEND_API_KEY);
  }
  return resend;
};

// Send email verification
export const sendEmailVerification = async (email, token) => {
  const verificationUrl = `${process.env.CLIENT_URL}/auth/verify-email?token=${token}`;

  if(process.env.NODE_ENV ==="development") {
    // console.log("Email verification URL:", verificationUrl);
    console.log("Send Email Verification");
    return;
  }



  try {
    const resendClient = getResendClient();
    const { data, error } = await resendClient.emails.send({
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: [email],
      subject: "Verify Your Email Address",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Email - DesignByte</title>
        </head>
        <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
          <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center;">
              <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 600;">DesignByte</h1>
              <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">Verify Your Email Address</p>
            </div>

            <!-- Content -->
            <div style="padding: 40px 30px;">
              <h2 style="color: #1a202c; margin: 0 0 16px 0; font-size: 24px; font-weight: 600;">Welcome to DesignByte!</h2>
              <p style="color: #4a5568; line-height: 1.6; margin: 0 0 24px 0; font-size: 16px;">
                Thank you for signing up! To get started and access all our premium templates and resources, please verify your email address by clicking the button below.
              </p>

              <!-- CTA Button -->
              <div style="text-align: center; margin: 32px 0;">
                <a href="${verificationUrl}"
                   style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: 600; font-size: 16px; box-shadow: 0 4px 14px rgba(102, 126, 234, 0.4);">
                  Verify Email Address
                </a>
              </div>

              <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 24px 0 0 0;">
                If the button doesn't work, you can copy and paste this link into your browser:
              </p>
              <p style="word-break: break-all; color: #4299e1; font-size: 14px; background-color: #f7fafc; padding: 12px; border-radius: 4px; border-left: 4px solid #4299e1;">
                ${verificationUrl}
              </p>

              <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
                <p style="color: #a0aec0; font-size: 12px; margin: 0;">
                  This verification link will expire in 24 hours for security reasons.
                </p>
                <p style="color: #a0aec0; font-size: 12px; margin: 8px 0 0 0;">
                  If you didn't create an account with DesignByte, you can safely ignore this email.
                </p>
              </div>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8fafc; padding: 24px 30px; text-align: center; border-top: 1px solid #e2e8f0;">
              <p style="color: #a0aec0; font-size: 14px; margin: 0;">
                © ${new Date().getFullYear()} DesignByte. All rights reserved.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
    });

    if (error) {
      console.error("Error sending email verification:", error);
      throw new Error("Failed to send verification email");
    }

    console.log("Email verification sent successfully:", data.id);
  } catch (error) {
    console.error("Error sending email verification:", error);
    throw new Error("Failed to send verification email");
  }
};

// Send password reset email
export const sendPasswordReset = async (email, token) => {
  const resetUrl = `${process.env.CLIENT_URL}/auth/reset-password?token=${token}`;

  try {
    const resendClient = getResendClient();
    const { data, error } = await resendClient.emails.send({
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: [email],
      subject: "Password Reset Request",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password - DesignByte</title>
        </head>
        <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
          <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); padding: 40px 20px; text-align: center;">
              <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 600;">DesignByte</h1>
              <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">Password Reset Request</p>
            </div>

            <!-- Content -->
            <div style="padding: 40px 30px;">
              <h2 style="color: #1a202c; margin: 0 0 16px 0; font-size: 24px; font-weight: 600;">Reset Your Password</h2>
              <p style="color: #4a5568; line-height: 1.6; margin: 0 0 24px 0; font-size: 16px;">
                We received a request to reset your password for your DesignByte account. Click the button below to create a new password.
              </p>

              <!-- CTA Button -->
              <div style="text-align: center; margin: 32px 0;">
                <a href="${resetUrl}"
                   style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: 600; font-size: 16px; box-shadow: 0 4px 14px rgba(245, 87, 108, 0.4);">
                  Reset Password
                </a>
              </div>

              <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 24px 0 0 0;">
                If the button doesn't work, you can copy and paste this link into your browser:
              </p>
              <p style="word-break: break-all; color: #4299e1; font-size: 14px; background-color: #f7fafc; padding: 12px; border-radius: 4px; border-left: 4px solid #4299e1;">
                ${resetUrl}
              </p>

              <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
                <p style="color: #e53e3e; font-size: 14px; margin: 0; font-weight: 600;">
                  ⚠️ This password reset link will expire in 1 hour for security reasons.
                </p>
                <p style="color: #a0aec0; font-size: 12px; margin: 16px 0 0 0;">
                  If you didn't request this password reset, please ignore this email. Your password will remain unchanged.
                </p>
              </div>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8fafc; padding: 24px 30px; text-align: center; border-top: 1px solid #e2e8f0;">
              <p style="color: #a0aec0; font-size: 14px; margin: 0;">
                © ${new Date().getFullYear()} DesignByte. All rights reserved.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
    });

    if (error) {
      console.error("Error sending password reset email:", error);
      throw new Error("Failed to send password reset email");
    }

    console.log("Password reset email sent successfully:", data.id);
  } catch (error) {
    console.error("Error sending password reset email:", error);
    throw new Error("Failed to send password reset email");
  }
};

// Send welcome email
export const sendWelcomeEmail = async (email, firstName) => {
  try {
    const resendClient = getResendClient();
    const { data, error } = await resendClient.emails.send({
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: [email],
      subject: "Welcome to DesignByte!",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to DesignByte!</title>
        </head>
        <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
          <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 40px 20px; text-align: center;">
              <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 600;">🎉 Welcome to DesignByte!</h1>
              <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">Your account is now verified and ready</p>
            </div>

            <!-- Content -->
            <div style="padding: 40px 30px;">
              <h2 style="color: #1a202c; margin: 0 0 16px 0; font-size: 24px; font-weight: 600;">Hello ${firstName}! 👋</h2>
              <p style="color: #4a5568; line-height: 1.6; margin: 0 0 24px 0; font-size: 16px;">
                Thank you for joining our community of designers and developers! Your email has been successfully verified, and you now have full access to all our premium templates and resources.
              </p>

              <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; padding: 24px; margin: 24px 0;">
                <h3 style="color: white; margin: 0 0 12px 0; font-size: 18px; font-weight: 600;">🚀 What's Next?</h3>
                <ul style="color: rgba(255, 255, 255, 0.9); margin: 0; padding-left: 20px; line-height: 1.6;">
                  <li>Browse our collection of premium templates</li>
                  <li>Download high-quality design resources</li>
                  <li>Access exclusive starter kits and themes</li>
                  <li>Join our community discussions</li>
                </ul>
              </div>

              <!-- CTA Button -->
              <div style="text-align: center; margin: 32px 0;">
                <a href="${process.env.CLIENT_URL}/templates"
                   style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: 600; font-size: 16px; box-shadow: 0 4px 14px rgba(79, 172, 254, 0.4);">
                  Explore Templates
                </a>
              </div>

              <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
                <p style="color: #4a5568; font-size: 14px; margin: 0 0 8px 0;">
                  Need help getting started? Our support team is here to help!
                </p>
                <p style="color: #4a5568; font-size: 14px; margin: 0;">
                  Happy designing! 🎨
                </p>
              </div>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8fafc; padding: 24px 30px; text-align: center; border-top: 1px solid #e2e8f0;">
              <p style="color: #a0aec0; font-size: 14px; margin: 0;">
                © ${new Date().getFullYear()} DesignByte. All rights reserved.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
    });

    if (error) {
      console.error("Error sending welcome email:", error);
      // Don't throw error for welcome email as it's not critical
      return;
    }

    console.log("Welcome email sent successfully:", data.id);
  } catch (error) {
    console.error("Error sending welcome email:", error);
    // Don't throw error for welcome email as it's not critical
  }
};