/**
 * @swagger
 * tags:
 *   name: Plans
 *   description: Plans management
 */

/**
 * @swagger
 * /plans:
 *   post:
 *     summary: Create a new plan
 *     tags: [Plans]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: The name of the plan
 *               description:
 *                 type: string
 *                 description: A short description of the plan
 *               price:
 *                 type: number
 *                 description: Price of the plan
 *               currency:
 *                 type: string
 *                 description: Currency of the price (default is USD)
 *                 default: "USD"
 *               duration:
 *                 type: string
 *                 description: Duration of the plan (monthly, yearly, or lifetime)
 *                 enum: ["monthly", "yearly", "lifetime"]
 *                 default: "monthly"
 *               isActive:
 *                 type: boolean
 *                 description: Indicates if the plan is active
 *                 default: true
 *               maxTemplates:
 *                 type: number
 *                 description: Maximum number of templates allowed
 *                 default: 5
 *               maxUsers:
 *                 type: number
 *                 description: Maximum number of users allowed
 *                 default: 1
 *               benefits:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of benefits included in the plan
 *               createdBy:
 *                 type: string
 *                 description: The ID of the admin who created the plan
 *     responses:
 *       201:
 *         description: Plan created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Whether the operation was successful
 *                 plan:
 *                   type: object
 *                   description: The created plan details
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /plans:
 *   get:
 *     summary: Get all plans
 *     tags: [Plans]
 *     responses:
 *       200:
 *         description: List of all plans
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 description: Plan object
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /plans/{id}:
 *   get:
 *     summary: Get plan by ID
 *     tags: [Plans]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the plan
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Plan found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 plan:
 *                   type: object
 *                   description: The details of the requested plan
 *       404:
 *         description: Plan not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /plans/{id}:
 *   put:
 *     summary: Update a plan by ID
 *     tags: [Plans]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the plan
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               price:
 *                 type: number
 *               currency:
 *                 type: string
 *               duration:
 *                 type: string
 *                 enum: ["monthly", "yearly", "lifetime"]
 *               isActive:
 *                 type: boolean
 *               maxTemplates:
 *                 type: number
 *               maxUsers:
 *                 type: number
 *               benefits:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Plan updated successfully
 *       404:
 *         description: Plan not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /plans/{id}:
 *   delete:
 *     summary: Delete a plan by ID
 *     tags: [Plans]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the plan
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Plan deleted successfully
 *       404:
 *         description: Plan not found
 *       500:
 *         description: Internal server error
 */
