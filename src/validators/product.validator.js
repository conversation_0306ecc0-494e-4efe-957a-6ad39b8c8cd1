import { body } from "express-validator";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

export const validateProduct = [
  body("name").notEmpty().withMessage("Name is required."),
  body("fullName").notEmpty().withMessage("Full name is required."),
  body("slug").notEmpty().withMessage("Slug is required."),
  body("category").notEmpty().withMessage("Category is required."),
  body("techStack")
    .notEmpty()
    .withMessage("Tech stack is required.")
    .isArray()
    .withMessage("Tech stack must be an array of strings.")
    .custom((techStack) => techStack.every((item) => typeof item === "string"))
    .withMessage("Each tech stack item must be a valid string."),
  body("price")
    .optional()
    .isNumeric()
    .withMessage("Price must be a number."),
  body("isPaid")
    .optional()
    .isBoolean()
    .withMessage("isPaid must be a boolean."),
  body("previewUrl")
    .optional()
    .isURL()
    .withMessage("Preview URL must be a valid URL."),
  body("githubUrl")
    .optional()
    .isURL()
    .withMessage("GitHub URL must be a valid URL."),
  body("paymentLink")
    .optional()
    .isURL()
    .withMessage("Payment link must be a valid URL."),
  body("status")
    .optional()
    .isIn(["active", "inactive", "archived"])
    .withMessage('Status must be one of: "active", "inactive", "archived".'),
  validateRequest,
];
