import { check, validationResult } from "express-validator";
import { 
  TRANSACTION_TYPES, 
  TRANSACTION_STATUSES, 
  PAYMENT_METHODS 
} from "../utils/constants.js";

export const validateCreateTransaction = [
  check("user")
    .notEmpty()
    .withMessage("User ID is required")
    .isMongoId()
    .withMessage("Invalid User ID format"),
  
  check("planId")
    .notEmpty()
    .withMessage("Plan ID is required")
    .isMongoId()
    .withMessage("Invalid Plan ID format"),
  
  check("couponId")
    .optional()
    .isMongoId()
    .withMessage("Invalid Coupon ID format"),
  
  check("type")
    .optional()
    .isIn(Object.values(TRANSACTION_TYPES))
    .withMessage("Invalid transaction type"),
  
  check("paymentMethod")
    .notEmpty()
    .withMessage("Payment method is required")
    .isIn(Object.values(PAYMENT_METHODS))
    .withMessage("Invalid payment method"),
  
  check("paymentGateway")
    .optional()
    .isIn(["stripe", "razorpay", "paypal", "manual"])
    .withMessage("Invalid payment gateway"),
  
  check("gatewaySubscriptionId")
    .optional()
    .isString()
    .withMessage("Gateway subscription ID must be a string"),
  
  check("metadata")
    .optional()
    .isObject()
    .withMessage("Metadata must be an object"),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false,
        errors: errors.array() 
      });
    }
    next();
  },
];

export const validateUpdateTransactionStatus = [
  check("status")
    .notEmpty()
    .withMessage("Status is required")
    .isIn(Object.values(TRANSACTION_STATUSES))
    .withMessage("Invalid transaction status"),
  
  check("gatewayTransactionId")
    .optional()
    .isString()
    .withMessage("Gateway transaction ID must be a string"),
  
  check("gatewayResponse")
    .optional()
    .isObject()
    .withMessage("Gateway response must be an object"),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false,
        errors: errors.array() 
      });
    }
    next();
  },
];

export const validateProcessRefund = [
  check("refundAmount")
    .optional()
    .isNumeric()
    .withMessage("Refund amount must be a number")
    .custom((value) => value >= 0)
    .withMessage("Refund amount must be greater than or equal to 0"),
  
  check("refundReason")
    .notEmpty()
    .withMessage("Refund reason is required")
    .isString()
    .withMessage("Refund reason must be a string"),
  
  check("refundedBy")
    .notEmpty()
    .withMessage("Refunded by user ID is required")
    .isMongoId()
    .withMessage("Invalid refunded by user ID format"),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false,
        errors: errors.array() 
      });
    }
    next();
  },
];

export const validateTransactionQuery = [
  check("page")
    .optional()
    .isInt({ min: 1 })
    .withMessage("Page must be a positive integer"),
  
  check("limit")
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage("Limit must be between 1 and 100"),
  
  check("status")
    .optional()
    .isIn(Object.values(TRANSACTION_STATUSES))
    .withMessage("Invalid status filter"),
  
  check("type")
    .optional()
    .isIn(Object.values(TRANSACTION_TYPES))
    .withMessage("Invalid type filter"),
  
  check("user")
    .optional()
    .isMongoId()
    .withMessage("Invalid user ID format"),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false,
        errors: errors.array() 
      });
    }
    next();
  },
];

export const validateTransactionStats = [
  check("startDate")
    .optional()
    .isISO8601()
    .withMessage("Invalid start date format"),
  
  check("endDate")
    .optional()
    .isISO8601()
    .withMessage("Invalid end date format")
    .custom((value, { req }) => {
      if (req.query.startDate && value) {
        const startDate = new Date(req.query.startDate);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error("End date must be after start date");
        }
      }
      return true;
    }),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false,
        errors: errors.array() 
      });
    }
    next();
  },
];
