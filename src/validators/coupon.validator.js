import { check, validationResult } from "express-validator";

export const validateCoupon = [
  check("code").notEmpty().withMessage("Coupon code is required"),
  check("discountType")
    .isIn(["percentage", "fixed"])
    .withMessage("Invalid discount type"),
  check("discountValue")
    .isNumeric()
    .withMessage("Discount value must be a number"),
  check("validFrom")
    .isISO8601()
    .toDate()
    .withMessage("Valid from date must be a valid date"),
  check("validUntil")
    .isISO8601()
    .toDate()
    .withMessage("Valid until date must be a valid date"),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];
