import { body } from "express-validator";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

export const validateFolder = [
  body("name")
    .notEmpty()
    .withMessage("Folder name is required.")
    .isString()
    .withMessage("Folder name must be a string.")
    .isLength({ min: 1, max: 255 })
    .withMessage("Folder name must be between 1 and 255 characters."),

  body("slug")
    .optional()
    .isString()
    .withMessage("Slug must be a string.")
    .matches(/^[a-z0-9-]+$/)
    .withMessage("Slug must contain only lowercase letters, numbers, and hyphens."),

  body("parent")
    .optional()
    .isMongoId()
    .withMessage("Parent must be a valid ObjectId."),

  body("title")
    .optional()
    .isString()
    .withMessage("Title must be a string.")
    .isLength({ max: 255 })
    .withMessage("Title must be less than 255 characters."),

  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string.")
    .isLength({ max: 1000 })
    .withMessage("Description must be less than 1000 characters."),

  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array.")
    .custom((tags) => tags.every((tag) => typeof tag === "string"))
    .withMessage("Each tag must be a string."),

  body("category")
    .optional()
    .isString()
    .withMessage("Category must be a string."),

  body("isPublic")
    .optional()
    .isBoolean()
    .withMessage("isPublic must be a boolean."),

  body("isFeatured")
    .optional()
    .isBoolean()
    .withMessage("isFeatured must be a boolean."),

  validateRequest,
];

export const validateFolderUpdate = [
  body("name")
    .optional()
    .isString()
    .withMessage("Folder name must be a string.")
    .isLength({ min: 1, max: 255 })
    .withMessage("Folder name must be between 1 and 255 characters."),

  body("slug")
    .optional()
    .isString()
    .withMessage("Slug must be a string.")
    .matches(/^[a-z0-9-]+$/)
    .withMessage("Slug must contain only lowercase letters, numbers, and hyphens."),

  body("parent")
    .optional()
    .isMongoId()
    .withMessage("Parent must be a valid ObjectId."),

  body("title")
    .optional()
    .isString()
    .withMessage("Title must be a string.")
    .isLength({ max: 255 })
    .withMessage("Title must be less than 255 characters."),

  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string.")
    .isLength({ max: 1000 })
    .withMessage("Description must be less than 1000 characters."),

  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array.")
    .custom((tags) => tags.every((tag) => typeof tag === "string"))
    .withMessage("Each tag must be a string."),

  body("category")
    .optional()
    .isString()
    .withMessage("Category must be a string."),

  body("isPublic")
    .optional()
    .isBoolean()
    .withMessage("isPublic must be a boolean."),

  body("isFeatured")
    .optional()
    .isBoolean()
    .withMessage("isFeatured must be a boolean."),

  validateRequest,
];
