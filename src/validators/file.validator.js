import { body } from "express-validator";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

export const validateFile = [
  body("name")
    .notEmpty()
    .withMessage("File name is required.")
    .isString()
    .withMessage("File name must be a string.")
    .isLength({ min: 1, max: 255 })
    .withMessage("File name must be between 1 and 255 characters."),

  body("originalName")
    .notEmpty()
    .withMessage("Original file name is required.")
    .isString()
    .withMessage("Original file name must be a string."),

  body("folder")
    .notEmpty()
    .withMessage("Folder is required.")
    .isMongoId()
    .withMessage("Folder must be a valid ObjectId."),

  body("url")
    .notEmpty()
    .withMessage("File URL is required.")
    .isURL()
    .withMessage("File URL must be a valid URL."),

  body("key")
    .optional()
    .isString()
    .withMessage("Storage key must be a string."),

  body("fileSize")
    .notEmpty()
    .withMessage("File size is required.")
    .isInt({ min: 0 })
    .withMessage("File size must be a positive integer."),

  body("mimeType")
    .notEmpty()
    .withMessage("MIME type is required.")
    .isString()
    .withMessage("MIME type must be a string."),

  body("fileType")
    .optional()
    .isIn(["image", "video", "audio", "document", "archive", "code", "other"])
    .withMessage("File type must be one of: image, video, audio, document, archive, code, other."),

  body("dimensions")
    .optional()
    .isObject()
    .withMessage("Dimensions must be an object."),

  body("dimensions.width")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Width must be a positive integer."),

  body("dimensions.height")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Height must be a positive integer."),

  body("source")
    .optional()
    .isIn(["S3", "UploadThing", "Unsplash", "External", "Local"])
    .withMessage("Source must be one of: S3, UploadThing, Unsplash, External, Local."),

  body("sourceDetails")
    .optional()
    .isString()
    .withMessage("Source details must be a string."),

  body("checksum")
    .optional()
    .isString()
    .withMessage("Checksum must be a string."),

  body("title")
    .optional()
    .isString()
    .withMessage("Title must be a string.")
    .isLength({ max: 255 })
    .withMessage("Title must be less than 255 characters."),

  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string.")
    .isLength({ max: 1000 })
    .withMessage("Description must be less than 1000 characters."),

  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array.")
    .custom((tags) => tags.every((tag) => typeof tag === "string"))
    .withMessage("Each tag must be a string."),

  body("category")
    .optional()
    .isString()
    .withMessage("Category must be a string."),

  body("isPublic")
    .optional()
    .isBoolean()
    .withMessage("isPublic must be a boolean."),

  body("isFeatured")
    .optional()
    .isBoolean()
    .withMessage("isFeatured must be a boolean."),

  validateRequest,
];

export const validateFileUpdate = [
  body("name")
    .optional()
    .isString()
    .withMessage("File name must be a string.")
    .isLength({ min: 1, max: 255 })
    .withMessage("File name must be between 1 and 255 characters."),

  body("folder")
    .optional()
    .isMongoId()
    .withMessage("Folder must be a valid ObjectId."),

  body("title")
    .optional()
    .isString()
    .withMessage("Title must be a string.")
    .isLength({ max: 255 })
    .withMessage("Title must be less than 255 characters."),

  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string.")
    .isLength({ max: 1000 })
    .withMessage("Description must be less than 1000 characters."),

  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array.")
    .custom((tags) => tags.every((tag) => typeof tag === "string"))
    .withMessage("Each tag must be a string."),

  body("category")
    .optional()
    .isString()
    .withMessage("Category must be a string."),

  body("isPublic")
    .optional()
    .isBoolean()
    .withMessage("isPublic must be a boolean."),

  body("isFeatured")
    .optional()
    .isBoolean()
    .withMessage("isFeatured must be a boolean."),

  validateRequest,
];
