import { body, param } from "express-validator";

// Create User Validator
export const createUserValidator = [
  body("email").isEmail().withMessage("Email is required and must be valid"),
  body("username").notEmpty().withMessage("Username is required"),
  body("password")
    .optional()
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters"),
  body("firstName").optional().trim().isLength({ min: 2, max: 50 }).withMessage("First name must be between 2 and 50 characters"),
  body("lastName").optional().trim().isLength({ min: 2, max: 50 }).withMessage("Last name must be between 2 and 50 characters"),
  body("role").optional().isIn(["user", "admin", "super_admin"]).withMessage("Invalid role"),
  body("profileImage").optional().isURL().withMessage("Profile image must be a valid URL"),
];

// Update User Validator
export const updateUserValidator = [
  body("email").optional().isEmail().withMessage("Email must be valid"),
  body("password")
    .optional()
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters"),
  body("firstName")
    .optional()
    .notEmpty()
    .withMessage("First name cannot be empty"),
  body("lastName")
    .optional()
    .notEmpty()
    .withMessage("Last name cannot be empty"),
];

// User ID Validator
export const userIdValidator = [
  param("id").isMongoId().withMessage("Invalid user ID"),
];

// Assign Subscription Validator
export const assignSubscriptionValidator = [
  body("userId").isMongoId().withMessage("Invalid user ID"),
  body("planId").isMongoId().withMessage("Invalid plan ID"),
  body("paymentMethod").notEmpty().withMessage("Payment method is required"),
];

// Update profile validation (for authenticated users)
export const updateProfileValidator = [
  body("firstName")
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("First name must be between 2 and 50 characters"),
  body("lastName")
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("Last name must be between 2 and 50 characters"),
  body("phoneNumber")
    .optional()
    .trim()
    .isLength({ min: 10, max: 20 })
    .withMessage("Phone number must be between 10 and 20 characters")
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage("Please provide a valid phone number"),
  body("profileImage")
    .optional()
    .trim()
    .custom((value) => {
      if (!value) return true; // Allow empty values
      // Allow URLs or empty strings
      const urlPattern = /^https?:\/\/.+/;
      if (!urlPattern.test(value)) {
        throw new Error("Profile image must be a valid URL starting with http:// or https://");
      }
      return true;
    }),
];

// Change password validation (for authenticated users)
export const changePasswordValidator = [
  body("currentPassword")
    .notEmpty()
    .withMessage("Current password is required"),
  body("newPassword")
    .isLength({ min: 6 })
    .withMessage("New password must be at least 6 characters long")
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage("New password must contain at least one uppercase letter, one lowercase letter, and one number"),
];
