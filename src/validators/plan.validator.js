import { body, validationResult } from "express-validator";
import { PLAN_TYPES, PLAN_TIERS } from "../models/plan.model.js";

export const validatePlan = [
  // Name validation
  body("name")
    .notEmpty()
    .withMessage("Plan name is required.")
    .isString()
    .withMessage("Plan name must be a string.")
    .isLength({ min: 2, max: 100 })
    .withMessage("Plan name must be between 2 and 100 characters."),

  // Price validation
  body("price")
    .notEmpty()
    .withMessage("Price is required.")
    .isNumeric()
    .withMessage("Price must be a number.")
    .custom((value) => value >= 0)
    .withMessage("Price must be greater than or equal to 0."),

  // Currency validation
  body("currency")
    .optional()
    .isString()
    .withMessage("Currency must be a string.")
    .isLength({ min: 3, max: 3 })
    .withMessage("Currency must be a valid 3-character ISO currency code (e.g., USD)."),

  // Duration validation
  body("duration")
    .optional()
    .isIn(["monthly", "yearly", "lifetime"])
    .withMessage(
      "Duration must be one of the following values: 'monthly', 'yearly', 'lifetime'."
    ),

  // Price ID validation (for payment gateway integration)
  body("priceId")
    .optional()
    .isString()
    .withMessage("Price ID must be a string.")
    .isLength({ min: 1, max: 255 })
    .withMessage("Price ID must be between 1 and 255 characters.")
    .custom((value, { req }) => {
      // Price ID is required for paid plans
      if (req.body.price > 0 && !value) {
        throw new Error("Price ID is required for paid plans.");
      }
      return true;
    }),

  // Plan Type validation
  body("planType")
    .optional()
    .isIn(Object.values(PLAN_TYPES))
    .withMessage(`Plan type must be one of: ${Object.values(PLAN_TYPES).join(", ")}.`),

  // Tier validation
  body("tier")
    .optional()
    .isIn(Object.values(PLAN_TIERS))
    .withMessage(`Tier must be one of: ${Object.values(PLAN_TIERS).join(", ")}.`),

  // Features validation
  body("features")
    .optional()
    .isArray()
    .withMessage("Features must be an array.")
    .custom((features) => {
      if (features && features.length > 0) {
        for (const feature of features) {
          if (!feature.name || typeof feature.name !== 'string') {
            throw new Error("Each feature must have a name (string).");
          }
          if (feature.included !== undefined && typeof feature.included !== 'boolean') {
            throw new Error("Feature 'included' field must be a boolean.");
          }
          if (feature.limit !== undefined && (!Number.isInteger(feature.limit) || feature.limit < -1)) {
            throw new Error("Feature 'limit' must be an integer >= -1 (-1 for unlimited).");
          }
        }
      }
      return true;
    }),

  // Limits validation
  body("limits")
    .optional()
    .isObject()
    .withMessage("Limits must be an object.")
    .custom((limits) => {
      if (limits) {
        const validLimitFields = ['downloads', 'projects', 'storage', 'apiCalls', 'teamMembers'];
        for (const [key, value] of Object.entries(limits)) {
          if (!validLimitFields.includes(key)) {
            throw new Error(`Invalid limit field: ${key}. Valid fields: ${validLimitFields.join(", ")}.`);
          }
          if (!Number.isInteger(value) || value < -1) {
            throw new Error(`Limit '${key}' must be an integer >= -1 (-1 for unlimited).`);
          }
        }
      }
      return true;
    }),

  // Benefits validation - accepts JSON objects (mixed type)
  body("benefits")
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        try {
          JSON.parse(value);
          return true;
        } catch (e) {
          return false;
        }
      }
      return typeof value === 'object';
    })
    .withMessage("Benefits must be a valid JSON object or string."),

  // Boolean field validations
  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("isActive must be a boolean."),

  body("isVisible")
    .optional()
    .isBoolean()
    .withMessage("isVisible must be a boolean."),

  body("isFeatured")
    .optional()
    .isBoolean()
    .withMessage("isFeatured must be a boolean."),

  // Sort order validation
  body("sortOrder")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Sort order must be a non-negative integer."),

  // Validate and handle errors
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }
    next();
  },
];
