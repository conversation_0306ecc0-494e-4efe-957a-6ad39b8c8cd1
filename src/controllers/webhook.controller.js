import { asyncHandler } from "../utils/asyncHandler.js";
import { subscriptionService } from "../services/subscription/SubscriptionService.js";
import { webhookRegistry } from "../services/webhooks/WebhookHandler.js";
import {
  webhookRetryHandler,
  webhookIdempotencyHandler
} from "../middlewares/webhook.middleware.js";
import {
  successResponse,
  errorResponse,
  badRequestResponse,
} from "../utils/responseHandler.js";

/**
 * Handle webhook from any payment gateway
 * @route POST /api/webhooks/:gateway
 */
export const handleWebhook = asyncHandler(async (req, res) => {
  const { gateway } = req.params;
  const payload = req.body;
  const signature = req.webhookSignature;
  const handler = req.webhookHandler;

  // Generate unique webhook ID for idempotency
  const webhookId = webhookIdempotencyHandler.generateWebhookId(gateway, payload);

  // Check if webhook was already processed
  if (webhookIdempotencyHandler.isProcessed(webhookId)) {
    const previousResult = webhookIdempotencyHandler.getPreviousResult(webhookId);
    console.log(`Webhook ${webhookId} already processed, returning cached result`);

    return successResponse(res, 'Webhook already processed', {
      cached: true,
      result: previousResult,
    });
  }

  // Process webhook with retry logic
  const result = await webhookRetryHandler.processWithRetry(
    async (data) => {
      // Parse webhook using gateway-specific handler
      const webhookData = await handler.processWebhook(data, signature);

      if (!webhookData.success) {
        throw new Error(webhookData.error);
      }
      // Process subscription logic
      return await subscriptionService.handleSubscriptionWebhook(webhookData);
    },
    payload
  );

  // Mark webhook as processed
  webhookIdempotencyHandler.markProcessed(webhookId, result);

  // Log successful processing
  console.log(`Webhook processed successfully:`, {
    gateway,
    webhookId,
    eventType: result.action,
    userId: result.user,
    transactionId: result.transaction,
  });

  successResponse(res, 'Webhook processed successfully', {
    gateway,
    result,
  });
}, (error, req, res) => {
  console.error(`Webhook processing failed for ${req.params.gateway}:`, error);

  // Return success to prevent webhook retries for business logic errors
  // Only return error for actual processing failures
  const shouldRetry = error.message.includes('database') ||
                     error.message.includes('network') ||
                     error.message.includes('timeout');

  if (shouldRetry) {
    errorResponse(res, 'Temporary processing error, will retry', { gateway: req.params.gateway }, 500);
  } else {
    successResponse(res, 'Webhook received but processing failed', {
      error: error.message,
      gateway: req.params.gateway,
    });
  }
});

/**
 * Handle Razorpay webhook specifically
 * @route POST /api/webhooks/razorpay
 */
export const handleRazorpayWebhook = asyncHandler(async (req, res) => {
  req.params.gateway = 'razorpay';
  return handleWebhook(req, res);
});

/**
 * Handle LemonSqueezy webhook specifically
 * @route POST /api/webhooks/lemonsqueezy
 */
export const handleLemonSqueezyWebhook = asyncHandler(async (req, res) => {
  req.params.gateway = 'lemonsqueezy';
  return handleWebhook(req, res);
});

/**
 * Handle Paddle webhook specifically
 * @route POST /api/webhooks/paddle
 */
export const handlePaddleWebhook = asyncHandler(async (req, res) => {
  req.params.gateway = 'paddle';
  return handleWebhook(req, res);
});

/**
 * Get webhook status and statistics
 * @route GET /api/webhooks/status
 */
export const getWebhookStatus = asyncHandler(async (req, res) => {
  const registeredGateways = webhookRegistry.getRegisteredGateways();

  const status = {
    registeredGateways,
    totalGateways: registeredGateways.length,
    supportedEvents: [
      'subscription.created',
      'subscription.activated',
      'subscription.updated',
      'subscription.cancelled',
      'subscription.expired',
      'subscription.paused',
      'subscription.resumed',
      'payment.success',
      'payment.failed',
      'payment.pending',
      'payment.refunded',
    ],
    configuration: {
      retryEnabled: true,
      maxRetries: 3,
      idempotencyEnabled: true,
      signatureVerification: true,
    },
  };

  successResponse(res, 'Webhook status fetched successfully', status);
});

/**
 * Test webhook endpoint for development
 * @route POST /api/webhooks/test/:gateway
 */
export const testWebhook = asyncHandler(async (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return errorResponse(res, 'Test endpoint not available in production', {}, 403);
  }

  const { gateway } = req.params;
  const testPayload = req.body;

  // Mock signature for testing
  req.webhookSignature = 'test-signature';

  // Get handler
  const handler = webhookRegistry.getHandler(gateway);
  if (!handler) {
    return badRequestResponse(res, `No handler registered for gateway: ${gateway}`);
  }

  req.webhookHandler = handler;

  try {
    // Process test webhook
    const webhookData = {
      success: true,
      gateway,
      eventType: testPayload.event || 'test',
      data: {
        subscription: testPayload.subscription || {},
        customer: testPayload.customer || {},
        payment: testPayload.payment || {},
        raw: testPayload,
      },
    };

    const result = await subscriptionService.handleSubscriptionWebhook(webhookData);

    successResponse(res, 'Test webhook processed', {
      gateway,
      testData: testPayload,
      result,
    });
  } catch (error) {
    errorResponse(res, error.message, { gateway }, 400);
  }
});

/**
 * Webhook health check
 * @route GET /api/webhooks/health
 */
export const webhookHealthCheck = asyncHandler(async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    registeredGateways: webhookRegistry.getRegisteredGateways(),
    environment: process.env.NODE_ENV,
  };

  successResponse(res, 'Webhook health check successful', health);
});

/**
 * Get webhook logs (for debugging)
 * @route GET /api/webhooks/logs
 */
export const getWebhookLogs = asyncHandler(async (req, res) => {
  // This would typically fetch from a logging service or database
  // For now, return a placeholder response
  successResponse(res, 'Webhook logs endpoint - implement with your logging solution', {
    recentWebhooks: [],
    totalProcessed: 0,
    successRate: 100,
  });
});
