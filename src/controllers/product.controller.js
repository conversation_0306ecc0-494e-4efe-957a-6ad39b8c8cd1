import Product from "../models/product.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { slugGenerator } from "../utils/slug-generator.js";
import {
  successResponse,
  notFoundResponse,
  badRequestResponse,
} from "../utils/responseHandler.js";

// Get all active products
export const getAllProducts = asyncHandler(async (req, res) => {
  const products = await Product.find({ status: "active" })
    .select("name fullName techStack category images slug price isPaid previewUrl paymentLink keyFeatures highlights isFeatured discount")
    .lean();
  successResponse(res, "Products fetched successfully.", products);
});

// Get products with cursor pagination
export const getProductsCursor = asyncHandler(async (req, res) => {
  const limit = Math.min(50, Math.max(1, parseInt(req.query.limit) || 10));
  const cursor = req.query.cursor;
  const query = cursor ? { _id: { $gt: cursor }, status: "active" } : { status: "active" };
  const products = await Product.find(query)
    .select("name fullName techStack category images slug price isPaid previewUrl paymentLink keyFeatures highlights isFeatured discount")
    .limit(limit)
    .sort({ _id: 1 })
    .lean();
  const hasNextPage = products.length === limit;
  successResponse(res, "Products fetched successfully.", {
    data: products,
    hasNextPage,
    nextCursor: hasNextPage ? products[products.length - 1]._id : null,
  });
});

// Get products by category
export const getProductsByCategory = asyncHandler(async (req, res) => {
  const { category } = req.params;
  const products = await Product.find({ category, status: "active" })
    .select("name fullName techStack category images slug price isPaid previewUrl paymentLink keyFeatures highlights isFeatured discount")
    .lean();
  successResponse(res, "Products fetched successfully by Category.", products);
});

// Get product by slug
export const getProductBySlug = asyncHandler(async (req, res) => {
  const { slug } = req.params;
  const product = await Product.findOne({ slug, status: "active" }).lean();
  if (!product) {
    return notFoundResponse(res, "Product not found");
  }
  successResponse(res, "Product fetched successfully.", product);
});

// Aggregate products (e.g., by category)
export const aggregateProducts = asyncHandler(async (req, res) => {
  const aggregation = await Product.aggregate([
    { $match: { status: "active" } },
    {
      $group: {
        _id: "$category",
        totalCount: { $sum: 1 },
        avgPrice: { $avg: "$price" },
      },
    },
  ]);
  successResponse(res, "Products aggregated successfully.", aggregation);
});

// Get subCategories with product count based on category
export const getSubCategoriesCountByCategory = asyncHandler(async (req, res) => {
  const { category } = req.params;
  const aggregation = await Product.aggregate([
    { $match: { status: "active", category } },
    { $unwind: "$subCategory" },
    {
      $group: {
        _id: "$subCategory",
        productCount: { $sum: 1 },
      },
    },
    { $sort: { productCount: -1 } },
  ]);
  successResponse(res, "Subcategories count fetched successfully.", aggregation);
});

// Get subcategories count by category
export const getSubCategoriesCount = asyncHandler(async (req, res) => {
  const aggregation = await Product.aggregate([
    { $match: { status: "active" } },
    {
      $group: {
        _id: "$category",
        subCategories: { $addToSet: "$subCategory" },
      },
    },
    {
      $project: {
        categoryId: "$_id",
        subCategoriesCount: { $size: "$subCategories" },
        subCategories: 1,
      },
    },
    {
      $lookup: {
        from: "categories",
        localField: "_id",
        foreignField: "_id",
        as: "categoryDetails",
      },
    },
    { $unwind: { path: "$categoryDetails", preserveNullAndEmptyArrays: true } },
    {
      $project: {
        categoryId: "$_id",
        subCategoriesCount: 1,
        subCategories: 1,
        categoryName: { $ifNull: ["$categoryDetails.name", "Uncategorized"] },
      },
    },
  ]);
  successResponse(res, "Subcategories count fetched successfully.", aggregation);
});

// Create product
export const createProduct = asyncHandler(async (req, res) => {
  const productData = req.body;
  const generatedSlug = productData.slug || slugGenerator(productData.fullName);
  const product = new Product({
    ...productData,
    slug: generatedSlug,
  });
  const createdProduct = await product.save();
  successResponse(res, "Product created successfully.", createdProduct);
});

// Update product
export const updateProduct = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;
  const updatedProduct = await Product.findByIdAndUpdate(id, updateData, {
    new: true,
  }).lean();
  if (!updatedProduct) {
    return notFoundResponse(res, "Product not found");
  }
  successResponse(res, "Product updated successfully.", updatedProduct);
});

// Delete product
export const deleteProduct = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const deletedProduct = await Product.findByIdAndDelete(id).lean();
  if (!deletedProduct) {
    return notFoundResponse(res, "Product not found");
  }
  successResponse(res, "Product deleted successfully.");
});

// Get product by ID
export const getProductById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const product = await Product.findById(id).lean();
  if (!product) {
    return notFoundResponse(res, "Product not found");
  }
  successResponse(res, "Product fetched successfully.", product);
});

// Get products with pagination
export const getProductsPagination = asyncHandler(async (req, res) => {
  const page = Math.max(1, parseInt(req.query.page) || 1);
  const limit = Math.min(50, Math.max(1, parseInt(req.query.limit) || 10));
  const skip = (page - 1) * limit;
  const total = await Product.countDocuments();
  const products = await Product.find()
    .skip(skip)
    .limit(limit)
    .lean();
  const totalPages = Math.ceil(total / limit);
  successResponse(res, "Products fetched successfully.", {
    data: products,
    totalDocuments: total,
    currentPage: page,
    totalPages: totalPages,
  });
});
