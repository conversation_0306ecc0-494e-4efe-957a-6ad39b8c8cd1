import User from "../models/user.model.js";
import Plan from "../models/plan.model.js";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import { generateToken } from "../utils/jwt.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import {
  successResponse,
  notFoundResponse,
  badRequestResponse,
  errorResponse,
} from "../utils/responseHandler.js";

// Create User (Admin only)
export const createUser = asyncHandler(async (req, res) => {
  const {
    email,
    password,
    firstName,
    lastName,
    username,
    profileImage,
    role,
    googleId,
  } = req.body;

  // Check if user exists
  const userExists = await User.findOne({ $or: [{ email }, { username }] }).lean();

  if (userExists) {
    return badRequestResponse(res, userExists.email === email
      ? "User with this email already exists"
      : "Username already taken");
  }

  // Hash password if provided
  let hashedPassword = null;
  if (password) {
    hashedPassword = await bcrypt.hash(password, 12);
  }

  // Create new user
  const newUser = new User({
    email,
    password: hashedPassword,
    firstName,
    lastName,
    username,
    profileImage,
    role,
    googleId,
    provider: googleId ? "google" : "email",
    isEmailVerified: googleId ? true : false, // Google users are pre-verified
  });

  const savedUser = await newUser.save();

  successResponse(res, "User created successfully", { user: savedUser });
});

// Get All Users
export const getUsers = asyncHandler(async (req, res) => {
  const users = await User.find({ isDeleted: false })
    .populate('currentPlan')
    .select('-password -emailVerificationToken -passwordResetToken')
    .lean();

  successResponse(res, "Users fetched successfully", { users });
});

// Get User with pagination
export const getUsersWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const total = await User.countDocuments({ isDeleted: false });
  const pages = Math.ceil(total / limit);

  const users = await User.find({ isDeleted: false })
    .skip(skip)
    .limit(limit)
    .lean();

  successResponse(res, "Users fetched successfully", {
    data: users,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

// Get Single User
export const getUserById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findById(id).lean();

  if (!user) {
    return notFoundResponse(res, "User not found");
  }

  successResponse(res, "User fetched successfully", { user });
});

// Update User
export const updateUser = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { password, ...updateData } = req.body;

  if (password) {
    updateData.password = await bcrypt.hash(password, 12); // Re-hash new password if provided
  }

  const updatedUser = await User.findByIdAndUpdate(id, updateData, { new: true }).lean();

  if (!updatedUser) {
    return notFoundResponse(res, "User not found");
  }

  successResponse(res, "User updated successfully", { user: updatedUser });
});

// Delete User (Soft Delete)
export const deleteUser = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedUser = await User.findByIdAndUpdate(id, { isDeleted: true }, { new: true }).lean();

  if (!deletedUser) {
    return notFoundResponse(res, "User not found");
  }

  successResponse(res, "User deleted successfully");
});

// Assign Subscription to User
export const assignSubscription = asyncHandler(async (req, res) => {
  const { userId, planId } = req.body;

  const user = await User.findById(userId).lean();

  if (!user) {
    return notFoundResponse(res, "User not found");
  }

  const plan = await Plan.findById(planId).lean();

  if (!plan) {
    return notFoundResponse(res, "Plan not found");
  }

  // Activate subscription using the user model method
  await User.findByIdAndUpdate(userId, { $set: { currentPlan: planId } });

  successResponse(res, "Subscription assigned successfully", {
    user: {
      id: user._id,
      isPro: user.isPro,
      isLifetimePro: user.isLifetimePro,
      subscriptionSummary: "Subscription summary details", // Assuming a method to get this
    },
  });
});

// Update current user profile (for authenticated users)
export const updateProfile = asyncHandler(async (req, res) => {
  const { firstName, lastName, phoneNumber, profileImage } = req.body;

  // Only allow updating specific profile fields
  const updateData = {};
  if (firstName !== undefined) updateData.firstName = firstName;
  if (lastName !== undefined) updateData.lastName = lastName;
  if (phoneNumber !== undefined) updateData.phoneNumber = phoneNumber;
  if (profileImage !== undefined) updateData.profileImage = profileImage;

  const updatedUser = await User.findByIdAndUpdate(
    req.user.id,
    updateData,
    { new: true }
  ).populate("currentPlan").select('-password -emailVerificationToken -passwordResetToken').lean();

  if (!updatedUser) {
    return notFoundResponse(res, "User not found");
  }

  successResponse(res, "Profile updated successfully", { user: updatedUser });
});

// Change password (for authenticated users)
export const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  // Get user with password
  const user = await User.findById(req.user.id);

  if (!user) {
    return notFoundResponse(res, "User not found");
  }

  // Check if user has a password (not OAuth only)
  if (!user.password) {
    return badRequestResponse(res, "Cannot change password for OAuth-only accounts");
  }

  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
  if (!isCurrentPasswordValid) {
    return badRequestResponse(res, "Current password is incorrect");
  }

  // Hash new password
  const salt = await bcrypt.genSalt(10);
  const hashedNewPassword = await bcrypt.hash(newPassword, salt);

  // Update password
  user.password = hashedNewPassword;
  await user.save();

  successResponse(res, "Password changed successfully");
});

// Bulk delete users (Admin only)
export const bulkDeleteUsers = asyncHandler(async (req, res) => {
  const { userIds } = req.body;

  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return badRequestResponse(res, "User IDs array is required");
  }

  const result = await User.updateMany(
    { _id: { $in: userIds } },
    { $set: { isDeleted: true } }
  );

  successResponse(res, `${result.modifiedCount} users deleted successfully`, {
    deletedCount: result.modifiedCount
  });
});

// Bulk update user roles (Admin only)
export const bulkUpdateUserRoles = asyncHandler(async (req, res) => {
  const { userIds, role } = req.body;

  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return badRequestResponse(res, "User IDs array is required");
  }

  if (!role) {
    return badRequestResponse(res, "Role is required");
  }

  const result = await User.updateMany(
    { _id: { $in: userIds } },
    { $set: { role } }
  );

  successResponse(res, `${result.modifiedCount} users updated successfully`, {
    updatedCount: result.modifiedCount
  });
});

// Bulk update user subscription status (Admin only)
export const bulkUpdateSubscriptions = asyncHandler(async (req, res) => {
  const { userIds, subscriptionData } = req.body;

  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return badRequestResponse(res, "User IDs array is required");
  }

  const updateData = {};
  if (subscriptionData.isPro !== undefined) updateData.isPro = subscriptionData.isPro;
  if (subscriptionData.isLifetimePro !== undefined) updateData.isLifetimePro = subscriptionData.isLifetimePro;
  if (subscriptionData.currentPlan) updateData.currentPlan = subscriptionData.currentPlan;
  if (subscriptionData.subscriptionStartDate) updateData.subscriptionStartDate = subscriptionData.subscriptionStartDate;
  if (subscriptionData.subscriptionEndDate) updateData.subscriptionEndDate = subscriptionData.subscriptionEndDate;

  const result = await User.updateMany(
    { _id: { $in: userIds } },
    { $set: updateData }
  );

  successResponse(res, `${result.modifiedCount} users updated successfully`, {
    updatedCount: result.modifiedCount
  });
});

// Bulk verify user emails (Admin only)
export const bulkVerifyEmails = asyncHandler(async (req, res) => {
  const { userIds } = req.body;

  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return badRequestResponse(res, "User IDs array is required");
  }

  const result = await User.updateMany(
    { _id: { $in: userIds } },
    { $set: { isEmailVerified: true } }
  );

  successResponse(res, `${result.modifiedCount} users verified successfully`, {
    verifiedCount: result.modifiedCount
  });
});

// Toggle user email verification status (Admin only)
export const toggleEmailVerification = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findById(id);
  if (!user) {
    return notFoundResponse(res, "User not found");
  }

  user.isEmailVerified = !user.isEmailVerified;
  await user.save();

  successResponse(res, `User email ${user.isEmailVerified ? 'verified' : 'unverified'} successfully`, {
    user: { id: user._id, isEmailVerified: user.isEmailVerified }
  });
});

// Suspend/Unsuspend user account (Admin only)
export const toggleUserSuspension = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findById(id);
  if (!user) {
    return notFoundResponse(res, "User not found");
  }

  user.isDeleted = !user.isDeleted;
  await user.save();

  successResponse(res, `User account ${user.isDeleted ? 'suspended' : 'activated'} successfully`, {
    user: { id: user._id, isDeleted: user.isDeleted }
  });
});

// Force password reset for user (Admin only)
export const forcePasswordReset = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findById(id);
  if (!user) {
    return notFoundResponse(res, "User not found");
  }

  // Generate password reset token
  const resetToken = crypto.randomBytes(32).toString('hex');
  const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');

  user.passwordResetToken = hashedToken;
  user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  await user.save();

  successResponse(res, "Password reset initiated successfully", {
    resetToken, // In production, this should be sent via email
    expiresAt: user.passwordResetExpires
  });
});

// Get user activity summary (Admin only)
export const getUserActivity = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findById(id).populate('currentPlan').lean();
  if (!user) {
    return notFoundResponse(res, "User not found");
  }

  const activity = {
    user: {
      id: user._id,
      email: user.email,
      username: user.username,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isEmailVerified: user.isEmailVerified,
      isDeleted: user.isDeleted,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    },
    subscription: {
      isPro: user.isPro,
      isLifetimePro: user.isLifetimePro,
      currentPlan: user.currentPlan,
      subscriptionStartDate: user.subscriptionStartDate,
      subscriptionEndDate: user.subscriptionEndDate
    },
    stats: {
      accountAge: user.createdAt ? Math.floor((new Date() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24)) : 0,
      lastActiveAgo: user.lastLogin ? Math.floor((new Date() - new Date(user.lastLogin)) / (1000 * 60 * 60 * 24)) : null,
      hasActiveSubscription: user.isPro || user.isLifetimePro
    }
  };

  successResponse(res, "User activity retrieved successfully", activity);
});

// Get user analytics (Admin only)
export const getUserAnalytics = asyncHandler(async (req, res) => {
  const totalUsers = await User.countDocuments({ isDeleted: false });
  const totalDeletedUsers = await User.countDocuments({ isDeleted: true });
  const verifiedUsers = await User.countDocuments({ isEmailVerified: true, isDeleted: false });
  const proUsers = await User.countDocuments({ isPro: true, isDeleted: false });
  const lifetimeProUsers = await User.countDocuments({ isLifetimePro: true, isDeleted: false });

  // Users by role
  const usersByRole = await User.aggregate([
    { $match: { isDeleted: false } },
    { $group: { _id: "$role", count: { $sum: 1 } } }
  ]);

  // Users by provider
  const usersByProvider = await User.aggregate([
    { $match: { isDeleted: false } },
    { $group: { _id: "$provider", count: { $sum: 1 } } }
  ]);

  // Recent registrations (last 30 days)
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const recentRegistrations = await User.countDocuments({
    createdAt: { $gte: thirtyDaysAgo },
    isDeleted: false
  });

  // Active users (logged in within last 30 days)
  const activeUsers = await User.countDocuments({
    lastLogin: { $gte: thirtyDaysAgo },
    isDeleted: false
  });

  const analytics = {
    overview: {
      totalUsers,
      totalDeletedUsers,
      verifiedUsers,
      proUsers,
      lifetimeProUsers,
      recentRegistrations,
      activeUsers
    },
    breakdown: {
      usersByRole: usersByRole.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      usersByProvider: usersByProvider.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {})
    },
    percentages: {
      verificationRate: totalUsers > 0 ? ((verifiedUsers / totalUsers) * 100).toFixed(2) : 0,
      proUserRate: totalUsers > 0 ? ((proUsers / totalUsers) * 100).toFixed(2) : 0,
      activeUserRate: totalUsers > 0 ? ((activeUsers / totalUsers) * 100).toFixed(2) : 0
    }
  };

  successResponse(res, "User analytics retrieved successfully", analytics);
});

// Get recent user activities (Admin only)
export const getRecentUserActivities = asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit) || 50;

  // Get recently created users
  const recentUsers = await User.find({ isDeleted: false })
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('_id email username firstName lastName createdAt lastLogin role')
    .lean();

  // Get recently active users
  const recentlyActive = await User.find({
    lastLogin: { $exists: true },
    isDeleted: false
  })
    .sort({ lastLogin: -1 })
    .limit(limit)
    .select('_id email username firstName lastName createdAt lastLogin role')
    .lean();

  const activities = {
    recentRegistrations: recentUsers,
    recentlyActive: recentlyActive
  };

  successResponse(res, "Recent user activities retrieved successfully", activities);
});
