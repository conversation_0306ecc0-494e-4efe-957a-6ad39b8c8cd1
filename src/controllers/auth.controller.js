import User from "../models/user.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import logger from "../utils/logger.js";
import bcrypt from "bcryptjs";
import {
  generateToken,
  generateEmailVerificationToken,
  generatePasswordResetToken,
  verifyToken,
  generateAccessAndRefreshTokens,
  setAccessTokenCookie,
} from "../utils/jwt.js";
import {
  sendEmailVerification,
  sendPasswordReset,
  sendWelcomeEmail,
} from "../services/email.service.js";
import passport from "../config/passport.js";
import {
  successResponse,
  errorResponse,
  unAuthorizedResponse,
  notFoundResponse,
  badRequestResponse,
  conflictResponse,
} from "../utils/responseHandler.js";
import { AccountSecurityService } from "../services/security/AccountSecurityService.js";
import UAParser from "ua-parser-js";
import ApiError from "../utils/ApiError.js";

// Register user with email and password
export const registerUser = asyncHandler(async (req, res) => {
  const { email, password, firstName, lastName, username } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [{ email }, { username }],
  });

  if (existingUser) {
    if (existingUser.email === email) {
      return conflictResponse(res, "User with this email already exists");
    } else {
      return conflictResponse(res, "Username already taken");
    }
  }

  // Hash password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);

  // Generate email verification token
  const emailVerificationToken = generateEmailVerificationToken();

  // Create user
  const user = await User.create({
    email,
    password: hashedPassword,
    firstName,
    lastName,
    username,
    emailVerificationToken,
    isEmailVerified: false,
  });

  // Send verification email
  try {
    await sendEmailVerification(email, emailVerificationToken);
  } catch (error) {
    logger.error("Failed to send verification email:", error);
    // Continue with registration even if email fails
  }

  // Generate JWT token
  const tokenData = generateToken(user._id);
  const token = tokenData.token

  successResponse(
    res,
    "User registered successfully. Please check your email for verification.",
    { token }
  );
});

// Login user with email and password
export const loginUser = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Check if user exists
  const user = await User.findOne({ email }).select("+password").populate("currentPlan");
  if (!user) {
    // Increment failed login attempts for security
    const tempUser = await User.findOne({ email });
    if (tempUser) {
      await tempUser.incrementLoginAttempts();
    }
    return unAuthorizedResponse(res, "Invalid email or password");
  }

  // Check if account is locked
  if (user.isAccountLocked()) {
    return res.status(423).json({
      success: false,
      message: "Account is temporarily locked due to multiple failed login attempts",
      lockedUntil: user.loginAttempts.lockedUntil,
      code: "ACCOUNT_LOCKED"
    });
  }

  // Check if account is suspended
  if (user.isCurrentlySuspended()) {
    return res.status(403).json({
      success: false,
      message: "Account is suspended",
      suspensionInfo: {
        reason: user.suspensionReason,
        suspendedAt: user.suspendedAt,
        expiresAt: user.suspensionExpiresAt
      },
      code: "ACCOUNT_SUSPENDED"
    });
  }

  // Check password
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    await user.incrementLoginAttempts();
    return unAuthorizedResponse(res, "Invalid email or password");
  }

  // Reset login attempts on successful login
  if (user.loginAttempts.count > 0) {
    await user.resetLoginAttempts();
  }

  try {
    // Gather session data
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';

    // Parse user agent for device info
    const parser = new UAParser(userAgent);
    const deviceInfo = parser.getResult();

    // Generate device fingerprint
    const deviceFingerprint = AccountSecurityService.generateDeviceFingerprint
      ? AccountSecurityService.generateDeviceFingerprint(deviceInfo, userAgent)
      : 'unknown';

    // Generate JWT token with session data
    const tokenData = generateToken(user._id, {
      deviceFingerprint,
      ipAddress
    });

    // Create session record
    const sessionResult = await AccountSecurityService.createSession(user, {
      jwtTokenId: tokenData.jwtTokenId,
      ipAddress,
      userAgent,
      expiresAt: tokenData.expiresAt,
      locationData: {
        // Add location data if available from IP geolocation service
        country: req.headers['cf-ipcountry'] || null,
        city: req.headers['cf-ipcity'] || null
      }
    });

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    const responseData = {
      token: tokenData.token,
      sessionId: sessionResult.session.sessionId,
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        subscriptionSummary: user.getSubscriptionSummary()
      }
    };

    // Include security warnings if any
    if (sessionResult.securityWarnings && sessionResult.securityWarnings.length > 0) {
      responseData.securityWarnings = sessionResult.securityWarnings;
    }

    successResponse(res, "Login successful", responseData);

  } catch (error) {
    logger.error("Error during login session creation:", error);

    // Fallback to basic token generation if session creation fails
    const tokenData = generateToken(user._id);
    user.lastLogin = new Date();
    await user.save();

    successResponse(res, "Login successful", {
      token: tokenData.token,
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        subscriptionSummary: user.getSubscriptionSummary()
      }
    });
  }
});

// Verify email
export const verifyEmail = asyncHandler(async (req, res) => {
  const { token } = req.body;

  try {
    // Verify the token
    const decoded = verifyToken(token);
    if (decoded.purpose !== "email_verification") {
      return badRequestResponse(res, "Invalid verification token");
    }

    // Find user with this token
    const user = await User.findOne({ emailVerificationToken: token });
    if (!user) {
      return badRequestResponse(res, "Invalid or expired verification token");
    }

    // Update user
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    await user.save();

    // Send welcome email
    try {
      await sendWelcomeEmail(user.email, user.firstName);
    } catch (error) {
      logger.error("Failed to send welcome email:", error);
    }

    successResponse(res, "Email verified successfully");
  } catch (error) {
    badRequestResponse(res, "Invalid or expired verification token");
  }
});

// Forgot password
export const forgotPassword = asyncHandler(async (req, res) => {
  const { email } = req.body;
  const user = await User.findOne({ email });

  if (!user) {
    return notFoundResponse(res, "User not found with this email");
  }

  // Generate reset token
  const resetToken = generatePasswordResetToken();

  // Save reset token and expiry
  user.passwordResetToken = resetToken;
  user.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
  await user.save();

  // Send reset email
  try {
    await sendPasswordReset(email, resetToken);
    successResponse(res, "Password reset email sent");
  } catch (error) {
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();
    logger.error("Failed to send password reset email:", error);
    return errorResponse(res, "Failed to send password reset email");
  }
});

// Reset password
export const resetPassword = asyncHandler(async (req, res) => {
  const { token, password } = req.body;

  try {
    // Verify the token
    const decoded = verifyToken(token);
    if (decoded.purpose !== "password_reset") {
      return badRequestResponse(res, "Invalid reset token");
    }

    // Find user with this token
    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: new Date() },
    });

    if (!user) {
      return badRequestResponse(res, "Invalid or expired reset token");
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update user
    user.password = hashedPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();

    successResponse(res, "Password reset successfully");
  } catch (error) {
    badRequestResponse(res, "Invalid or expired reset token");
  }
});

// Get current user
export const getCurrentUser = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id).populate("currentPlan").select('-password -emailVerificationToken -passwordResetToken').lean();

  if (!user) {
    return notFoundResponse(res, "User not found");
  }
  successResponse(res, "User retrieved successfully", user);
});

// Resend email verification
export const resendEmailVerification = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id);

  if (!user) {
    return notFoundResponse(res, "User not found");
  }

  if (user.isEmailVerified) {
    return badRequestResponse(res, "Email is already verified");
  }

  // Generate new verification token
  const emailVerificationToken = generateEmailVerificationToken();
  user.emailVerificationToken = emailVerificationToken;
  await user.save();

  // Send verification email
  try {
    await sendEmailVerification(user.email, emailVerificationToken);
    successResponse(res, "Verification email sent successfully");
  } catch (error) {
    logger.error("Failed to send verification email:", error);
    return errorResponse(res, "Failed to send verification email");
  }
});

// Logout user
export const logoutUser = asyncHandler(async (req, res) => {
  try {
    // Get session ID from headers
    const sessionId = req.headers['x-session-id'];

    if (sessionId) {
      // Terminate the session
      await AccountSecurityService.terminateSession(sessionId, "user_logout");
      logger.info(`User logged out with session termination: ${req.user?._id}`);
    }

    // Clear cookie
    res.cookie("token", "", {
      expires: new Date(0),
      httpOnly: true,
    });

    successResponse(res, "Logged out successfully");
  } catch (error) {
    logger.error("Error during logout:", error);

    // Still clear cookie even if session termination fails
    res.cookie("token", "", {
      expires: new Date(0),
      httpOnly: true,
    });

    successResponse(res, "Logged out successfully");
  }
});

// Logout from all devices
export const logoutAllDevices = asyncHandler(async (req, res) => {
  try {
    if (!req.user) {
      return unAuthorizedResponse(res, "Authentication required");
    }

    // Terminate all sessions for the user
    const terminatedCount = await AccountSecurityService.terminateAllUserSessions(
      req.user._id,
      "user_logout_all"
    );

    logger.info(`User logged out from all devices: ${req.user._id}`, {
      terminatedSessions: terminatedCount
    });

    // Clear cookie
    res.cookie("token", "", {
      expires: new Date(0),
      httpOnly: true,
    });

    successResponse(res, "Logged out from all devices successfully", {
      terminatedSessions: terminatedCount
    });
  } catch (error) {
    logger.error("Error during logout all devices:", error);
    return errorResponse(res, "Failed to logout from all devices");
  }
});

// Google OAuth Controller
export const googleAuth = asyncHandler(async (req, res, next) => {
  try {
    const clientRedirect = req.query.clientRedirect || process.env.CLIENT_URL || process.env.FRONTEND_URL; // Default redirect URL
    const state = JSON.stringify({ clientRedirect }); // Encode clientRedirect into state

    // Initiate Google authentication
    passport.authenticate("google", {
      scope: ["profile", "email"], // Request user's profile and email
      state, // Pass the state parameter
    })(req, res, next);
  } catch (error) {
    logger.error("[GOOGLE] Error initiating authentication:", error);
    throw new ApiError(500, "Internal server error");
  }
});

// Google Callback Controller
export const googleCallback = asyncHandler(async (req, res, next) => {
  // eslint-disable-next-line consistent-return
  passport.authenticate("google", async (err, user) => {
    if (err || !user) {
      logger.error("[GOOGLE] Authentication failed:", err);
      const redirectUrl = `${process.env.CLIENT_URL || process.env.FRONTEND_URL}/auth?status=failure`;
      return res.redirect(redirectUrl);
    }

    try {
      // Parse the state parameter
      // const state = JSON.parse(req.query.state || "{}");
      // const clientRedirect = state.clientRedirect || process.env.CLIENT_URL || process.env.FRONTEND_URL;
      const clientRedirect = process.env.CLIENT_URL || process.env.FRONTEND_URL;

      // Generate access token
      const { accessToken } = await generateAccessAndRefreshTokens(user._id);

      // Set access token in cookie
      setAccessTokenCookie(res, accessToken);

      logger.info("[GOOGLE] User authenticated successfully:", user.username);

      // Redirect to the client-provided URL with the access token
      return res.redirect(`${clientRedirect}/auth?status=success&token=${accessToken}`);
    } catch (error) {
      logger.error("[GOOGLE] Error generating access token:", error);
      const redirectUrl = `${process.env.CLIENT_URL || process.env.FRONTEND_URL}/auth?status=failure`;
      return res.redirect(redirectUrl);
    }
  })(req, res, next);
});

// GitHub OAuth Controller
export const githubAuth = asyncHandler(async (req, res, next) => {
  try {
    const clientRedirect = req.query.clientRedirect || process.env.CLIENT_URL || process.env.FRONTEND_URL; // Default redirect URL
    const state = JSON.stringify({ clientRedirect }); // Encode clientRedirect into state

    // Initiate GitHub authentication
    passport.authenticate("github", {
      scope: ["user:email"], // Request user's email
      state, // Pass the state parameter
    })(req, res, next);
  } catch (error) {
    logger.error("[GITHUB] Error initiating authentication:", error);
    throw new ApiError(500, "Internal server error");
  }
});

// GitHub Callback Controller
export const githubCallback = asyncHandler(async (req, res, next) => {
  // eslint-disable-next-line consistent-return
  passport.authenticate("github", async (err, user) => {
    if (err || !user) {
      logger.error("[GITHUB] Authentication failed:", err);
      const redirectUrl = `${process.env.CLIENT_URL || process.env.FRONTEND_URL}/auth?status=failure`;
      return res.redirect(redirectUrl);
    }

    try {
      // Parse the state parameter
      const state = JSON.parse(req.query.state || "{}");
      logger.info("state", state);
      const clientRedirect = process.env.CLIENT_URL || process.env.FRONTEND_URL;
      // const clientRedirect = state.clientRedirect || process.env.CLIENT_URL || process.env.FRONTEND_URL;
      logger.info("clientRedirect", clientRedirect);

      // Generate access token
      const { accessToken } = await generateAccessAndRefreshTokens(user._id);

      // Set access token in cookie
      setAccessTokenCookie(res, accessToken);

      logger.info("[GITHUB] User authenticated successfully:", user.username);

      // Redirect to the client-provided URL with the access token
      return res.redirect(`${clientRedirect}/auth?status=success&token=${accessToken}`);
    } catch (error) {
      logger.error("[GITHUB] Error generating access token:", error);
      const redirectUrl = `${process.env.CLIENT_URL || process.env.FRONTEND_URL}/auth?status=failure`;
      return res.redirect(redirectUrl);
    }
  })(req, res, next);
});
