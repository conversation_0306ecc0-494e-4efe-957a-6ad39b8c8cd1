import Coupon from "../models/coupon.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  badRequestResponse,
} from "../utils/responseHandler.js";

/**
 * Create a new coupon
 */
export const createCoupon = asyncHandler(async (req, res) => {
  const { code, discountType, discountValue, maxRedemptions, validFrom, validUntil, isActive } = req.body;

  // Check if coupon code already exists
  const existingCoupon = await Coupon.findOne({ code }).lean();
  if (existingCoupon) {
    return badRequestResponse(res, "Coupon code already exists");
  }

  const newCoupon = new Coupon({
    code,
    discountType,
    discountValue,
    maxRedemptions,
    validFrom,
    validUntil,
    isActive,
  });

  const savedCoupon = await newCoupon.save();
  successResponse(res, "Coupon created successfully", savedCoupon);
});

/**
 * Get all coupons
 */
export const getCoupons = asyncHandler(async (req, res) => {
  // Use lean() to get plain JavaScript objects
  const coupons = await Coupon.find().lean();
  successResponse(res, "Coupons fetched successfully", coupons);
});

/**
 * Get all coupons with pagination
 */
export const getCouponsWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  // Use countDocuments without any additional queries
  const total = await Coupon.countDocuments();

  // Use lean() to get plain JavaScript objects and limit the fields
  const coupons = await Coupon.find()
    .skip(skip)
    .limit(limit)
    .lean();

  const pages = Math.ceil(total / limit);

  successResponse(res, "Coupons fetched successfully", {
    coupons,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

/**
 * Get a single coupon by ID
 */
export const getCouponById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Use lean() to get a plain JavaScript object
  const coupon = await Coupon.findById(id).lean();
  if (!coupon) {
    return notFoundResponse(res, "Coupon not found");
  }

  successResponse(res, "Coupon fetched successfully", coupon);
});

/**
 * Update a coupon
 */
export const updateCoupon = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  // Use findByIdAndUpdate with lean() and return the updated document
  const updatedCoupon = await Coupon.findByIdAndUpdate(id, updates, { new: true }).lean();
  if (!updatedCoupon) {
    return notFoundResponse(res, "Coupon not found");
  }

  successResponse(res, "Coupon updated successfully", updatedCoupon);
});

/**
 * Delete a coupon
 */
export const deleteCoupon = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Use findByIdAndDelete with lean()
  const deletedCoupon = await Coupon.findByIdAndDelete(id).lean();
  if (!deletedCoupon) {
    return notFoundResponse(res, "Coupon not found");
  }

  successResponse(res, "Coupon deleted successfully");
});
