import Folder from "../models/folder.model.js";
import File from "../models/file.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  badRequestResponse,
} from "../utils/responseHandler.js";

// Create a new folder
export const createFolder = asyncHandler(async (req, res) => {
  const { name, slug, parent, title, description, tags, category } = req.body;
  const userId = req.user.id;

  const newFolder = new Folder({
    name,
    slug: slug || name.toLowerCase().replace(/\s+/g, '-'),
    parent: parent || null,
    title,
    description,
    tags,
    category,
    createdBy: userId,
    isPublic: true,
  });

  const savedFolder = await newFolder.save();
  successResponse(res, "Folder created successfully.", savedFolder);
});

// Upload/create a new file
export const createFile = asyncHandler(async (req, res) => {
  const {
    name,
    originalName,
    folder,
    url,
    key,
    fileSize,
    mimeType,
    title,
    description,
    tags,
    category,
    isPublic,
    isFeatured,
    dimensions,
    source,
    sourceDetails,
    checksum,
  } = req.body;

  const userId = req.user.id;

  const newFile = new File({
    name,
    originalName: originalName || name,
    folder: folder,
    url,
    key,
    fileSize,
    mimeType,
    title,
    description,
    tags,
    category,
    isPublic: isPublic !== undefined ? isPublic : true,
    isFeatured: isFeatured || false,
    dimensions,
    source: source || "S3",
    sourceDetails,
    checksum,
    createdBy: userId,
    uploadedBy: userId,
  });

  const savedFile = await newFile.save();

  // Update folder statistics
  if (folder) {
    await Folder.findByIdAndUpdate(folder, {
      $inc: { totalFiles: 1, totalSize: fileSize || 0 }
    });
  }

  successResponse(res, "File created successfully.", savedFile);
});

// Get folder contents (files and subfolders)
export const getFolderContents = asyncHandler(async (req, res) => {
  const { folderId } = req.params;
  const { page = 1, limit = 20, type } = req.query;
  const skip = (page - 1) * limit;
  const parentId = folderId === "root" ? null : folderId;

  let folders = [];
  let files = [];
  let total = 0;

  if (!type || type === "folder") {
    const folderQuery = { parent: parentId, isDeleted: false };
    folders = await Folder.find(folderQuery)
      .skip(type === "folder" ? skip : 0)
      .limit(type === "folder" ? parseInt(limit) : parseInt(limit))
      .sort({ name: 1 })
      .lean();

    if (type === "folder") {
      total = await Folder.countDocuments(folderQuery);
    }
  }

  if (!type || type === "file") {
    const fileQuery = { folder: parentId, isDeleted: false };
    files = await File.find(fileQuery)
      .skip(type === "file" ? skip : 0)
      .limit(type === "file" ? parseInt(limit) : parseInt(limit))
      .sort({ name: 1 })
      .lean();

    if (type === "file") {
      total = await File.countDocuments(fileQuery);
    }
  }

  if (!type) {
    // Combined results
    const folderCount = await Folder.countDocuments({ parent: parentId, isDeleted: false });
    const fileCount = await File.countDocuments({ folder: parentId, isDeleted: false });
    total = folderCount + fileCount;
  }

  const items = [...folders, ...files];
  const totalPages = Math.ceil(total / limit);

  successResponse(res, "Folder contents fetched successfully.", {
    data: items,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});

// Search files and folders
export const searchFileSystem = asyncHandler(async (req, res) => {
  const { query, type, tags, category, page = 1, limit = 20 } = req.query;
  const skip = (page - 1) * limit;

  const searchFilter = {
    isDeleted: false,
    $or: [
      { name: { $regex: query || "", $options: "i" } },
      { title: { $regex: query || "", $options: "i" } },
      { description: { $regex: query || "", $options: "i" } },
    ],
  };

  if (tags) {
    searchFilter.tags = { $in: tags.split(",") };
  }

  if (category) {
    searchFilter.category = category;
  }

  let results = [];
  let total = 0;

  if (!type || type === "folder") {
    const folders = await Folder.find(searchFilter)
      .skip(type === "folder" ? skip : 0)
      .limit(type === "folder" ? parseInt(limit) : parseInt(limit))
      .sort({ name: 1 })
      .lean();

    results = [...results, ...folders];

    if (type === "folder") {
      total = await Folder.countDocuments(searchFilter);
    }
  }

  if (!type || type === "file") {
    const files = await File.find(searchFilter)
      .skip(type === "file" ? skip : 0)
      .limit(type === "file" ? parseInt(limit) : parseInt(limit))
      .sort({ name: 1 })
      .lean();

    results = [...results, ...files];

    if (type === "file") {
      total = await File.countDocuments(searchFilter);
    }
  }

  if (!type) {
    const folderCount = await Folder.countDocuments(searchFilter);
    const fileCount = await File.countDocuments(searchFilter);
    total = folderCount + fileCount;
  }

  const totalPages = Math.ceil(total / limit);

  successResponse(res, "Search results fetched successfully.", {
    data: results,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});

// Get item by ID
export const getItemById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Try to find as folder first, then as file
  let item = await Folder.findById(id).lean();
  let itemType = "folder";

  if (!item) {
    item = await File.findById(id).lean();
    itemType = "file";
  }

  if (!item || item.isDeleted) {
    return notFoundResponse(res, "Item not found.");
  }

  // Increment view count for files
  if (itemType === "file") {
    await File.findByIdAndUpdate(id, { $inc: { views: 1 } });
  }

  successResponse(res, "Item fetched successfully.", { ...item, itemType });
});

// Update item (folder or file)
export const updateItem = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Remove fields that shouldn't be updated directly
  delete updateData.createdBy;
  delete updateData.path;
  delete updateData.uploadedBy;

  // Try to find as folder first, then as file
  let updatedItem = await Folder.findByIdAndUpdate(
    id,
    updateData,
    { new: true, runValidators: true }
  ).lean();

  let itemType = "folder";

  if (!updatedItem) {
    updatedItem = await File.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).lean();
    itemType = "file";
  }

  if (!updatedItem || updatedItem.isDeleted) {
    return notFoundResponse(res, "Item not found.");
  }

  successResponse(res, "Item updated successfully.", { ...updatedItem, itemType });
});

// Soft delete items
export const deleteItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return badRequestResponse(res, "Please provide an array of IDs to delete.");
  }

  const updateOperations = ids.map(id => ({
    updateOne: {
      filter: { _id: id, isDeleted: false },
      update: { isDeleted: true }
    }
  }));

  const folderUpdateResult = await Folder.bulkWrite(updateOperations);
  const fileUpdateResult = await File.bulkWrite(updateOperations);

  const deletedCount = folderUpdateResult.modifiedCount + fileUpdateResult.modifiedCount;

  successResponse(res, `${deletedCount} item(s) deleted successfully.`);
});

// Permanently delete items
export const permanentDeleteItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return badRequestResponse(res, "Please provide an array of IDs to delete.");
  }

  const deletedFolders = await Folder.deleteMany({ _id: { $in: ids } });
  const deletedFiles = await File.deleteMany({ _id: { $in: ids } });

  const totalDeleted = deletedFolders.deletedCount + deletedFiles.deletedCount;

  successResponse(res, `${totalDeleted} item(s) permanently deleted.`);
});

// Restore deleted items
export const restoreItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return badRequestResponse(res, "Please provide an array of IDs to restore.");
  }

  const updateOperations = ids.map(id => ({
    updateOne: {
      filter: { _id: id, isDeleted: true },
      update: { isDeleted: false }
    }
  }));

  const restoredFolders = await Folder.bulkWrite(updateOperations);
  const restoredFiles = await File.bulkWrite(updateOperations);

  const totalRestored = restoredFolders.modifiedCount + restoredFiles.modifiedCount;

  successResponse(res, `${totalRestored} item(s) restored successfully.`);
});

// Get files by type (images, documents, etc.)
export const getFilesByType = asyncHandler(async (req, res) => {
  const { mimeType, fileType, page = 1, limit = 20 } = req.query;
  const skip = (page - 1) * limit;

  if (!mimeType && !fileType) {
    return badRequestResponse(res, "MIME type or file type is required.");
  }

  const query = {
    isDeleted: false,
    isPublic: true,
  };

  if (mimeType) {
    query.mimeType = { $regex: mimeType, $options: "i" };
  }

  if (fileType) {
    query.fileType = fileType;
  }

  const files = await File.find(query)
    .skip(skip)
    .limit(parseInt(limit))
    .sort({ createdAt: -1 })
    .lean();

  const total = await File.countDocuments(query);
  const totalPages = Math.ceil(total / limit);

  successResponse(res, "Files fetched successfully.", {
    data: files,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});
