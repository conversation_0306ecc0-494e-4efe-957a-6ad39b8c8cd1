import Transaction from "../models/transaction.model.js";
import Plan from "../models/plan.model.js";
import Coupon from "../models/coupon.model.js";
import User from "../models/user.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { TRANSACTION_TYPES, TRANSACTION_STATUSES } from "../utils/constants.js";
import {
  successResponse,
  notFoundResponse,
  badRequestResponse,
} from "../utils/responseHandler.js";

/**
 * Create a new transaction (for subscription purchase/renewal)
 */
export const createTransaction = asyncHandler(async (req, res) => {
  const {
    user,
    planId,
    couponId,
    paymentMethod,
    paymentGateway,
    gatewaySubscriptionId,
    type = TRANSACTION_TYPES.SUBSCRIPTION_PURCHASE,
    metadata = {},
  } = req.body;

  const plan = await Plan.findById(planId).lean();
  if (!plan) {
    return notFoundResponse(res, "Plan not found");
  }

  let coupon = null;
  if (couponId) {
    coupon = await Coupon.findById(couponId).lean();
    if (!coupon) {
      return notFoundResponse(res, "Coupon not found");
    }
  }

  const transaction = await Transaction.createSubscriptionTransaction({
    user,
    plan,
    coupon,
    paymentMethod,
    paymentGateway,
    gatewaySubscriptionId,
    type,
    metadata,
  });

  if (coupon) {
    await Coupon.findByIdAndUpdate(couponId, { $inc: { currentRedemptions: 1 } });
  }

  successResponse(res, "Transaction created successfully.", transaction);
});

/**
 * Get all transactions with pagination
 */
export const getTransactions = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const { status, type, user } = req.query;

  // Build filter object
  const filter = {};
  if (status) filter.status = status;
  if (type) filter.type = type;
  if (user) filter.user = user;

  const total = await Transaction.countDocuments(filter);
  const pages = Math.ceil(total / limit);

  const transactions = await Transaction.find(filter)
    .populate("user", "firstName lastName email")
    .populate("plan", "name price duration")
    .populate("subscription", "startDate endDate")
    .populate("coupon", "code discountType discountValue")
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .lean();

  successResponse(res, "Transactions fetched successfully.", {
    data: transactions,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

/**
 * Get transaction by ID
 */
export const getTransactionById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const transaction = await Transaction.findById(id)
    .populate("user", "firstName lastName email")
    .populate("plan", "name price duration benefits")
    .populate("coupon", "code discountType discountValue")
    .lean();

  if (!transaction) {
    return notFoundResponse(res, "Transaction not found");
  }

  successResponse(res, "Transaction fetched successfully.", transaction);
});

/**
 * Update transaction status
 */
export const updateTransactionStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status, gatewayTransactionId, gatewayResponse } = req.body;

  const transaction = await Transaction.findByIdAndUpdate(
    id,
    {
      status,
      ...(gatewayTransactionId && { gatewayTransactionId }),
      ...(gatewayResponse && { gatewayResponse }),
    },
    { new: true }
  ).lean();

  if (!transaction) {
    return notFoundResponse(res, "Transaction not found");
  }

  successResponse(res, "Transaction updated successfully.", transaction);
});

/**
 * Process refund
 */
export const processRefund = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { refundAmount, refundReason, refundedBy } = req.body;

  const transaction = await Transaction.findById(id);
  if (!transaction) {
    return notFoundResponse(res, "Transaction not found");
  }

  if (transaction.status !== TRANSACTION_STATUSES.COMPLETED) {
    return badRequestResponse(res, "Can only refund completed transactions");
  }

  if (transaction.refund?.isRefunded) {
    return badRequestResponse(res, "Transaction already refunded");
  }

  // Update refund details
  const updatedTransaction = await Transaction.findByIdAndUpdate(
    id,
    {
      "refund.isRefunded": true,
      "refund.refundAmount": refundAmount || transaction.amount,
      "refund.refundReason": refundReason,
      "refund.refundDate": new Date(),
      "refund.refundedBy": refundedBy,
      status: TRANSACTION_STATUSES.REFUNDED,
    },
    { new: true }
  ).lean();

  successResponse(res, "Refund processed successfully.", updatedTransaction);
});

/**
 * Get user's transaction history
 */
export const getUserTransactions = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const total = await Transaction.countDocuments({ user: userId });
  const pages = Math.ceil(total / limit);

  const transactions = await Transaction.find({ user: userId })
    .populate("plan", "name price duration")
    .populate("subscription", "startDate endDate")
    .populate("coupon", "code discountType discountValue")
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .lean();

  successResponse(res, "User transactions fetched successfully.", {
    data: transactions,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

/**
 * Get transaction analytics/stats
 */
export const getTransactionStats = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;

  const matchStage = {};
  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  const stats = await Transaction.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalTransactions: { $sum: 1 },
        totalRevenue: { $sum: "$amount" },
        totalDiscounts: { $sum: "$discountAmount" },
        avgTransactionValue: { $avg: "$amount" },
        completedTransactions: {
          $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
        },
        failedTransactions: {
          $sum: { $cond: [{ $eq: ["$status", "failed"] }, 1, 0] },
        },
        freeTransactions: {
          $sum: { $cond: [{ $eq: ["$amount", 0] }, 1, 0] },
        },
      },
    },
  ]);

  const result = stats[0] || {
    totalTransactions: 0,
    totalRevenue: 0,
    totalDiscounts: 0,
    avgTransactionValue: 0,
    completedTransactions: 0,
    failedTransactions: 0,
    freeTransactions: 0,
  };

  successResponse(res, "Transaction stats fetched successfully.", result);
});
