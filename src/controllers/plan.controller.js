import Plan, { PLAN_TYPES, PLAN_TIERS } from "../models/plan.model.js";
import { PlanBusinessService } from "../services/plan/PlanBusinessService.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import {
  successResponse,
  notFoundResponse,
  badRequestResponse,
  errorResponse,
} from "../utils/responseHandler.js";

/**
 * Create a new plan
 */
export const createPlan = asyncHandler(async (req, res) => {
  const {
    name,
    description,
    price,
    currency,
    duration,
    priceId,
    planType,
    tier,
    isActive,
    isVisible,
    isFeatured,
    benefits,
    features,
    limits,
    sortOrder,
    metadata,
  } = req.body;

  // Set createdBy to the authenticated user
  const createdBy = req.user._id;

  // Validate business logic
  if (price > 0 && !priceId) {
    return badRequestResponse(res, "Price ID is required for paid plans");
  }

  // Set default plan type and tier based on price
  const finalPlanType = planType || (price === 0 ? PLAN_TYPES.FREE : PLAN_TYPES.INDIVIDUAL);
  const finalTier = tier || (price === 0 ? PLAN_TIERS.FREE : PLAN_TIERS.BASIC);

  try {
    const newPlan = new Plan({
      name,
      description,
      price,
      currency: currency || "USD",
      duration: duration || "monthly",
      priceId,
      planType: finalPlanType,
      tier: finalTier,
      isActive: isActive !== undefined ? isActive : true,
      isVisible: isVisible !== undefined ? isVisible : true,
      isFeatured: isFeatured || false,
      benefits,
      features: features || [],
      limits: {
        downloads: -1,
        projects: -1,
        storage: -1,
        apiCalls: -1,
        teamMembers: 1,
        ...limits
      },
      sortOrder: sortOrder || 0,
      metadata: metadata || {},
      createdBy,
    });

    const savedPlan = await newPlan.save();
    successResponse(res, "Plan created successfully.", savedPlan);
  } catch (error) {
    if (error.code === 11000) {
      // Handle duplicate key error
      const field = Object.keys(error.keyPattern)[0];
      return badRequestResponse(res, `A plan with this ${field} already exists`);
    }
    throw error;
  }
});



/**
 * Get all plans with filtering and sorting
 */
export const getPlans = asyncHandler(async (req, res) => {
  const {
    planType,
    tier,
    isActive,
    isVisible,
    duration,
    includeInactive = false
  } = req.query;

  // Build query
  const query = {};

  if (planType) query.planType = planType;
  if (tier) query.tier = tier;
  if (duration) query.duration = duration;
  if (isActive !== undefined) query.isActive = isActive === 'true';
  if (isVisible !== undefined) query.isVisible = isVisible === 'true';

  // Default to active plans only unless explicitly requested
  if (!includeInactive) {
    query.isActive = true;
  }

  const plans = await Plan.find(query)
    .populate("createdBy", "firstName lastName email")
    .sort({ sortOrder: 1, price: 1 })
    .lean();

  successResponse(res, "Plans fetched successfully.", plans);
});

/**
 * Get visible plans for pricing page (public endpoint)
 */
export const getVisiblePlans = asyncHandler(async (req, res) => {
  const plans = await Plan.getVisiblePlans();
  successResponse(res, "Visible plans fetched successfully.", plans);
});

/**
 * Get plans by type (e.g., individual, startup)
 */
export const getPlansByType = asyncHandler(async (req, res) => {
  const { type } = req.params;
  const { duration } = req.query;

  if (!Object.values(PLAN_TYPES).includes(type)) {
    return badRequestResponse(res, `Invalid plan type. Valid types: ${Object.values(PLAN_TYPES).join(", ")}`);
  }

  const options = {};
  if (duration) options.duration = duration;

  const plans = await Plan.getByType(type, options);
  successResponse(res, `${type} plans fetched successfully.`, plans);
});

/**
 * Get a single plan by ID
 */
export const getPlanById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const plan = await Plan.findById(id)
    .populate("createdBy", "name email")
    .lean();

  if (!plan) {
    return notFoundResponse(res, "Plan not found");
  }

  successResponse(res, "Plan fetched successfully.", plan);
});

/**
 * Update a plan
 */
export const updatePlan = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  // Validate business logic for updates
  if (updates.price > 0 && !updates.priceId) {
    const existingPlan = await Plan.findById(id);
    if (!existingPlan) {
      return notFoundResponse(res, "Plan not found");
    }
    if (!existingPlan.priceId) {
      return badRequestResponse(res, "Price ID is required for paid plans");
    }
  }

  // Remove fields that shouldn't be updated directly
  delete updates.createdBy;
  delete updates.createdAt;

  try {
    const updatedPlan = await Plan.findByIdAndUpdate(id, updates, {
      new: true,
      runValidators: true,
    }).populate("createdBy", "firstName lastName email");

    if (!updatedPlan) {
      return notFoundResponse(res, "Plan not found");
    }

    successResponse(res, "Plan updated successfully.", updatedPlan);
  } catch (error) {
    if (error.code === 11000) {
      // Handle duplicate key error
      const field = Object.keys(error.keyPattern)[0];
      return badRequestResponse(res, `A plan with this ${field} already exists`);
    }
    throw error;
  }
});

/**
 * Delete a plan
 */
export const deletePlan = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedPlan = await Plan.findByIdAndDelete(id).lean();

  if (!deletedPlan) {
    return notFoundResponse(res, "Plan not found");
  }

  successResponse(res, "Plan deleted successfully.");
});

/**
 * Get pricing plans for public pricing page (includes UI-only Enterprise)
 */
export const getPricingPlans = asyncHandler(async (req, res) => {
  try {
    const plans = await PlanBusinessService.getPricingPlans();
    successResponse(res, "Pricing plans fetched successfully.", plans);
  } catch (error) {
    return errorResponse(res, "Failed to fetch pricing plans");
  }
});

/**
 * Initialize default plans (admin only)
 */
export const initializeDefaultPlans = asyncHandler(async (req, res) => {
  try {
    await PlanBusinessService.initializeDefaultPlans();
    successResponse(res, "Default plans initialized successfully.");
  } catch (error) {
    return errorResponse(res, "Failed to initialize default plans");
  }
});

/**
 * Get recommended plan for user
 */
export const getRecommendedPlan = asyncHandler(async (req, res) => {
  try {
    const recommendation = await PlanBusinessService.getRecommendedPlan(req.user);
    successResponse(res, "Plan recommendation generated successfully.", recommendation);
  } catch (error) {
    return errorResponse(res, "Failed to generate plan recommendation");
  }
});

/**
 * Validate plan change for user
 */
export const validatePlanChange = asyncHandler(async (req, res) => {
  const { planId } = req.body;

  try {
    const newPlan = await Plan.findById(planId);
    if (!newPlan) {
      return notFoundResponse(res, "Plan not found");
    }

    const validation = await PlanBusinessService.validatePlanChange(req.user, newPlan);
    successResponse(res, "Plan change validation completed.", validation);
  } catch (error) {
    return errorResponse(res, "Failed to validate plan change");
  }
});
