import { AccountSecurityService } from "../services/security/AccountSecurityService.js";
import { SessionCleanupService } from "../services/cleanup/SessionCleanupService.js";
import UserSession from "../models/userSession.model.js";
import User from "../models/user.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  badRequestResponse,
  unAuthorizedResponse,
} from "../utils/responseHandler.js";
import logger from "../utils/logger.js";

/**
 * Get current user's active sessions
 */
export const getUserSessions = asyncHandler(async (req, res) => {
  try {
    const sessionSummary = await AccountSecurityService.getUserSessionSummary(req.user._id);
    successResponse(res, "User sessions retrieved successfully", sessionSummary);
  } catch (error) {
    logger.error("Error getting user sessions:", error);
    return errorResponse(res, "Failed to retrieve user sessions");
  }
});

/**
 * Terminate a specific session
 */
export const terminateSession = asyncHandler(async (req, res) => {
  const { sessionId } = req.params;
  const { reason = "user_terminated" } = req.body;

  try {
    // Verify the session belongs to the current user
    const session = await UserSession.findOne({ sessionId }).populate("user");
    
    if (!session) {
      return notFoundResponse(res, "Session not found");
    }

    if (session.user._id.toString() !== req.user._id.toString()) {
      return unAuthorizedResponse(res, "You can only terminate your own sessions");
    }

    const success = await AccountSecurityService.terminateSession(sessionId, reason);
    
    if (success) {
      successResponse(res, "Session terminated successfully");
    } else {
      return errorResponse(res, "Failed to terminate session");
    }
  } catch (error) {
    logger.error("Error terminating session:", error);
    return errorResponse(res, "Failed to terminate session");
  }
});

/**
 * Terminate all sessions except current one
 */
export const terminateOtherSessions = asyncHandler(async (req, res) => {
  const currentSessionId = req.headers['x-session-id'];
  
  try {
    const activeSessions = await UserSession.getActiveSessions(req.user._id);
    let terminatedCount = 0;

    for (const session of activeSessions) {
      if (session.sessionId !== currentSessionId) {
        await AccountSecurityService.terminateSession(session.sessionId, "user_terminated_others");
        terminatedCount++;
      }
    }

    successResponse(res, "Other sessions terminated successfully", {
      terminatedCount
    });
  } catch (error) {
    logger.error("Error terminating other sessions:", error);
    return errorResponse(res, "Failed to terminate other sessions");
  }
});

/**
 * Get account sharing detection results
 */
export const getAccountSharingStatus = asyncHandler(async (req, res) => {
  try {
    const sharingDetection = await AccountSecurityService.detectAccountSharing(req.user._id);
    
    successResponse(res, "Account sharing status retrieved", {
      isSharing: sharingDetection.sharing,
      confidence: sharingDetection.confidence,
      indicators: sharingDetection.indicators,
      activeSessionCount: sharingDetection.sessions.length,
      securityFlags: req.user.securityFlags
    });
  } catch (error) {
    logger.error("Error getting account sharing status:", error);
    return errorResponse(res, "Failed to retrieve account sharing status");
  }
});

/**
 * Report suspicious activity
 */
export const reportSuspiciousActivity = asyncHandler(async (req, res) => {
  const { description, sessionId } = req.body;

  try {
    if (sessionId) {
      const session = await UserSession.findOne({ sessionId });
      if (session && session.user.toString() === req.user._id.toString()) {
        await session.markSuspicious(`User reported: ${description}`);
      }
    }

    // Flag user account
    await req.user.flagSuspiciousActivity();

    logger.warn(`Suspicious activity reported by user ${req.user._id}`, {
      description,
      sessionId,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    });

    successResponse(res, "Suspicious activity reported successfully");
  } catch (error) {
    logger.error("Error reporting suspicious activity:", error);
    return errorResponse(res, "Failed to report suspicious activity");
  }
});

// Admin endpoints

/**
 * Get all sessions (admin only)
 */
export const getAllSessions = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    userId, 
    ipAddress,
    suspicious = false 
  } = req.query;

  try {
    const query = {};
    
    if (status) query.status = status;
    if (userId) query.user = userId;
    if (ipAddress) query['networkInfo.ipAddress'] = ipAddress;
    if (suspicious === 'true') query['securityFlags.isSuspiciousActivity'] = true;

    const sessions = await UserSession.find(query)
      .populate("user", "email username firstName lastName")
      .sort({ loginTime: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();

    const total = await UserSession.countDocuments(query);

    successResponse(res, "Sessions retrieved successfully", {
      sessions,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalSessions: total,
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    logger.error("Error getting all sessions:", error);
    return errorResponse(res, "Failed to retrieve sessions");
  }
});

/**
 * Terminate user session (admin only)
 */
export const adminTerminateSession = asyncHandler(async (req, res) => {
  const { sessionId } = req.params;
  const { reason = "admin_terminated" } = req.body;

  try {
    const session = await UserSession.findOne({ sessionId }).populate("user");
    
    if (!session) {
      return notFoundResponse(res, "Session not found");
    }

    const success = await AccountSecurityService.terminateSession(sessionId, reason);
    
    if (success) {
      logger.info(`Admin terminated session: ${sessionId}`, {
        adminId: req.user._id,
        targetUserId: session.user._id,
        reason
      });

      successResponse(res, "Session terminated successfully");
    } else {
      return errorResponse(res, "Failed to terminate session");
    }
  } catch (error) {
    logger.error("Error in admin session termination:", error);
    return errorResponse(res, "Failed to terminate session");
  }
});

/**
 * Suspend user account (admin only)
 */
export const suspendUserAccount = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { reason, expiresAt, notes } = req.body;

  try {
    const user = await User.findById(userId);
    
    if (!user) {
      return notFoundResponse(res, "User not found");
    }

    if (user.isCurrentlySuspended()) {
      return badRequestResponse(res, "User is already suspended");
    }

    const expirationDate = expiresAt ? new Date(expiresAt) : null;
    await user.suspendAccount(reason, req.user._id, expirationDate, notes);

    // Terminate all user sessions
    await AccountSecurityService.terminateAllUserSessions(userId, "account_suspended");

    logger.info(`User account suspended: ${userId}`, {
      adminId: req.user._id,
      reason,
      expiresAt: expirationDate,
      notes
    });

    successResponse(res, "User account suspended successfully", {
      suspensionInfo: {
        reason,
        suspendedAt: user.suspendedAt,
        expiresAt: user.suspensionExpiresAt
      }
    });
  } catch (error) {
    logger.error("Error suspending user account:", error);
    return errorResponse(res, "Failed to suspend user account");
  }
});

/**
 * Lift user suspension (admin only)
 */
export const liftUserSuspension = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { notes } = req.body;

  try {
    const user = await User.findById(userId);
    
    if (!user) {
      return notFoundResponse(res, "User not found");
    }

    if (!user.isCurrentlySuspended()) {
      return badRequestResponse(res, "User is not currently suspended");
    }

    await user.liftSuspension(req.user._id, notes);

    logger.info(`User suspension lifted: ${userId}`, {
      adminId: req.user._id,
      notes
    });

    successResponse(res, "User suspension lifted successfully");
  } catch (error) {
    logger.error("Error lifting user suspension:", error);
    return errorResponse(res, "Failed to lift user suspension");
  }
});

/**
 * Get security dashboard data (admin only)
 */
export const getSecurityDashboard = asyncHandler(async (req, res) => {
  try {
    const [
      activeSessions,
      suspiciousSessions,
      suspendedUsers,
      usersWithSecurityFlags,
      recentLogins
    ] = await Promise.all([
      UserSession.countDocuments({ status: 'active' }),
      UserSession.countDocuments({ 'securityFlags.isSuspiciousActivity': true }),
      User.countDocuments({ isSuspended: true }),
      User.countDocuments({
        $or: [
          { 'securityFlags.accountSharingDetected': true },
          { 'securityFlags.suspiciousActivityDetected': true },
          { 'securityFlags.multipleFailedLogins': true }
        ]
      }),
      UserSession.find({ status: 'active' })
        .populate("user", "email username")
        .sort({ loginTime: -1 })
        .limit(10)
        .lean()
    ]);

    successResponse(res, "Security dashboard data retrieved", {
      stats: {
        activeSessions,
        suspiciousSessions,
        suspendedUsers,
        usersWithSecurityFlags
      },
      recentLogins
    });
  } catch (error) {
    logger.error("Error getting security dashboard:", error);
    return errorResponse(res, "Failed to retrieve security dashboard data");
  }
});

/**
 * Manual session cleanup (admin only)
 */
export const manualSessionCleanup = asyncHandler(async (req, res) => {
  try {
    const result = await SessionCleanupService.manualCleanup();
    successResponse(res, "Session cleanup completed successfully", result);
  } catch (error) {
    logger.error("Manual session cleanup failed:", error);
    return errorResponse(res, error.message || "Failed to run session cleanup");
  }
});

/**
 * Get session cleanup statistics (admin only)
 */
export const getCleanupStats = asyncHandler(async (req, res) => {
  try {
    const stats = SessionCleanupService.getCleanupStats();
    successResponse(res, "Cleanup statistics retrieved successfully", stats);
  } catch (error) {
    logger.error("Failed to get cleanup stats:", error);
    return errorResponse(res, "Failed to get cleanup statistics");
  }
});
