/**
 * System Health Controller
 * Provides comprehensive system health and status information
 */

import mongoose from "mongoose";
import { asyncHandler } from "../utils/asyncHandler.js";
import { successResponse, errorResponse } from "../utils/responseHandler.js";
import { webhookRegistry } from "../services/webhooks/index.js";
import os from "os";

/**
 * Get comprehensive system health information
 * @route GET /api/system/health
 * @access Public
 */
export const getSystemHealth = asyncHandler(async (req, res) => {
  try {
    // Get database connection status
    const dbState = mongoose.connection.readyState;
    const dbStatus = {
      0: "disconnected",
      1: "connected", 
      2: "connecting",
      3: "disconnecting"
    };

    // Get system metrics
    const systemMetrics = {
      uptime: process.uptime(),
      memory: {
        used: process.memoryUsage().heapUsed / 1024 / 1024, // MB
        total: process.memoryUsage().heapTotal / 1024 / 1024, // MB
        external: process.memoryUsage().external / 1024 / 1024, // MB
        rss: process.memoryUsage().rss / 1024 / 1024 // MB
      },
      cpu: {
        loadAverage: os.loadavg(),
        cpuCount: os.cpus().length
      },
      system: {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        totalMemory: os.totalmem() / 1024 / 1024 / 1024, // GB
        freeMemory: os.freemem() / 1024 / 1024 / 1024 // GB
      }
    };

    // Get payment gateway status
    const registeredGateways = webhookRegistry.getRegisteredGateways();
    
    // Determine overall system status
    let status = "healthy";
    const issues = [];

    // Check database connection
    if (dbState !== 1) {
      status = "degraded";
      issues.push(`Database connection: ${dbStatus[dbState]}`);
    }

    // Check memory usage (warn if over 80%)
    const memoryUsagePercent = (systemMetrics.memory.used / systemMetrics.memory.total) * 100;
    if (memoryUsagePercent > 80) {
      status = status === "healthy" ? "warning" : status;
      issues.push(`High memory usage: ${memoryUsagePercent.toFixed(1)}%`);
    }

    // Check if no payment gateways are configured
    if (registeredGateways.length === 0) {
      status = status === "healthy" ? "warning" : status;
      issues.push("No payment gateways configured");
    }

    const healthData = {
      status,
      timestamp: new Date().toISOString(),
      uptime: systemMetrics.uptime,
      environment: process.env.NODE_ENV || "development",
      database: {
        status: dbStatus[dbState],
        host: mongoose.connection.host || "unknown"
      },
      paymentGateways: {
        registered: registeredGateways,
        count: registeredGateways.length
      },
      system: systemMetrics.system,
      performance: {
        memory: systemMetrics.memory,
        cpu: systemMetrics.cpu
      },
      issues: issues.length > 0 ? issues : null
    };

    successResponse(res, "System health retrieved successfully", healthData);
  } catch (error) {
    console.error("Error getting system health:", error);
    errorResponse(res, "Failed to retrieve system health", { error: error.message }, 500);
  }
});

/**
 * Get database health information
 * @route GET /api/system/database
 * @access Private (Admin only)
 */
export const getDatabaseHealth = asyncHandler(async (req, res) => {
  try {
    const dbState = mongoose.connection.readyState;
    const dbStatus = {
      0: "disconnected",
      1: "connected",
      2: "connecting", 
      3: "disconnecting"
    };

    // Get database stats if connected
    let dbStats = null;
    if (dbState === 1) {
      try {
        const db = mongoose.connection.db;
        const stats = await db.stats();
        
        dbStats = {
          collections: stats.collections,
          dataSize: Math.round(stats.dataSize / 1024 / 1024 * 100) / 100, // MB
          storageSize: Math.round(stats.storageSize / 1024 / 1024 * 100) / 100, // MB
          indexSize: Math.round(stats.indexSize / 1024 / 1024 * 100) / 100, // MB
          objects: stats.objects,
          avgObjSize: Math.round(stats.avgObjSize * 100) / 100 // bytes
        };
      } catch (statsError) {
        console.warn("Could not retrieve database stats:", statsError.message);
      }
    }

    const healthData = {
      status: dbStatus[dbState],
      connected: dbState === 1,
      host: mongoose.connection.host || "unknown",
      name: mongoose.connection.name || "unknown",
      readyState: dbState,
      stats: dbStats,
      timestamp: new Date().toISOString()
    };

    successResponse(res, "Database health retrieved successfully", healthData);
  } catch (error) {
    console.error("Error getting database health:", error);
    errorResponse(res, "Failed to retrieve database health", { error: error.message }, 500);
  }
});

/**
 * Get system performance metrics
 * @route GET /api/system/metrics
 * @access Private (Admin only)
 */
export const getSystemMetrics = asyncHandler(async (req, res) => {
  try {
    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: {
        process: process.uptime(),
        system: os.uptime()
      },
      memory: {
        process: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024 * 100) / 100, // MB
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024 * 100) / 100, // MB
          external: Math.round(process.memoryUsage().external / 1024 / 1024 * 100) / 100, // MB
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024 * 100) / 100 // MB
        },
        system: {
          total: Math.round(os.totalmem() / 1024 / 1024 / 1024 * 100) / 100, // GB
          free: Math.round(os.freemem() / 1024 / 1024 / 1024 * 100) / 100, // GB
          used: Math.round((os.totalmem() - os.freemem()) / 1024 / 1024 / 1024 * 100) / 100 // GB
        }
      },
      cpu: {
        loadAverage: os.loadavg(),
        cpuCount: os.cpus().length,
        model: os.cpus()[0]?.model || "unknown"
      },
      network: {
        hostname: os.hostname(),
        platform: os.platform(),
        arch: os.arch(),
        release: os.release()
      },
      node: {
        version: process.version,
        pid: process.pid,
        title: process.title
      }
    };

    successResponse(res, "System metrics retrieved successfully", metrics);
  } catch (error) {
    console.error("Error getting system metrics:", error);
    errorResponse(res, "Failed to retrieve system metrics", { error: error.message }, 500);
  }
});
