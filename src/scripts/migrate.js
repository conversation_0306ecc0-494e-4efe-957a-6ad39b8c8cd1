#!/usr/bin/env node

import dotenv from "dotenv";
import { migrateFrom<PERSON>lerkAuth } from "../migrations/001_remove_clerk_auth.js";

// Load environment variables
dotenv.config();

async function runMigration() {
  try {
    console.log("=".repeat(50));
    console.log("DESIGNBYTE DATABASE MIGRATION");
    console.log("=".repeat(50));
    console.log("This will migrate from Clerk authentication to custom auth");
    console.log("Make sure you have a database backup before proceeding!");
    console.log("=".repeat(50));

    // Run the migration
    const result = await migrateFromClerkAuth();
    
    console.log("=".repeat(50));
    console.log("MIGRATION COMPLETED SUCCESSFULLY!");
    console.log("=".repeat(50));
    console.log(`Users updated: ${result.usersUpdated}`);
    console.log(`Transactions updated: ${result.transactionsUpdated}`);
    console.log("=".repeat(50));
    console.log("IMPORTANT NOTES:");
    console.log("1. Users with email auth may need to reset their passwords");
    console.log("2. Check the migration logs for any temporary passwords");
    console.log("3. Test the authentication system thoroughly");
    console.log("4. Update your environment variables:");
    console.log("   - Add JWT_SECRET");
    console.log("   - Add SMTP settings for email");
    console.log("   - Add Google OAuth credentials");
    console.log("   - Add Upstash Redis credentials");
    console.log("=".repeat(50));

  } catch (error) {
    console.error("=".repeat(50));
    console.error("MIGRATION FAILED!");
    console.error("=".repeat(50));
    console.error("Error:", error.message);
    console.error("=".repeat(50));
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigration();
}
