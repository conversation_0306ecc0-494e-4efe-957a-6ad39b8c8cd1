import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/user.model.js';
import logger from '../utils/logger.js';

dotenv.config();

/**
 * Migration script to update existing users to new OAuth schema
 * This script will:
 * 1. Remove the old 'provider' and 'googleId' fields
 * 2. Migrate existing Google users to oauthProviders array
 * 3. Update field names from is_email_verified to isEmailVerified
 */
async function migrateUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info('Connected to MongoDB for migration');

    // Find all users with old schema fields
    const usersToMigrate = await User.find({
      $or: [
        { provider: { $exists: true } },
        { googleId: { $exists: true } },
      ]
    });

    logger.info(`Found ${usersToMigrate.length} users to migrate`);

    let migratedCount = 0;
    let errorCount = 0;

    for (const user of usersToMigrate) {
      try {
        const updates = {};
        const unsetFields = {};

        // Migrate Google OAuth users
        if (user.googleId && user.provider === 'google') {
          // Check if oauthProviders already has Google entry
          const hasGoogleProvider = user.oauthProviders?.some(oauth => oauth.provider === 'google');
          
          if (!hasGoogleProvider) {
            updates.$push = {
              oauthProviders: {
                provider: 'google',
                provider_id: user.googleId,
                linked_at: user.createdAt || new Date()
              }
            };
          }
          
          // Remove old fields
          unsetFields.googleId = '';
          unsetFields.provider = '';
        } else if (user.provider) {
          // Remove provider field for email users
          unsetFields.provider = '';
        }

        // Migrate is_email_verified to isEmailVerified
        if (user.is_email_verified !== undefined) {
          updates.isEmailVerified = user.is_email_verified;
          unsetFields.is_email_verified = '';
        }

        // Apply updates
        const updateQuery = {};
        if (Object.keys(updates).length > 0) {
          Object.assign(updateQuery, updates);
        }
        if (Object.keys(unsetFields).length > 0) {
          updateQuery.$unset = unsetFields;
        }

        if (Object.keys(updateQuery).length > 0) {
          await User.updateOne({ _id: user._id }, updateQuery);
          migratedCount++;
          logger.info(`Migrated user: ${user.email || user.username}`);
        }

      } catch (error) {
        errorCount++;
        logger.error(`Error migrating user ${user.email || user.username}:`, error.message);
      }
    }

    logger.info(`Migration completed: ${migratedCount} users migrated, ${errorCount} errors`);

    // Verify migration by checking for any remaining old fields
    const remainingOldFields = await User.find({
      $or: [
        { provider: { $exists: true } },
        { googleId: { $exists: true } },
        { is_email_verified: { $exists: true } }
      ]
    });

    if (remainingOldFields.length > 0) {
      logger.warn(`Warning: ${remainingOldFields.length} users still have old schema fields`);
    } else {
      logger.info('✅ All users successfully migrated to new schema');
    }

  } catch (error) {
    logger.error('Migration failed:', error);
  } finally {
    await mongoose.connection.close();
    logger.info('Database connection closed');
  }
}



export default migrateUsers;
