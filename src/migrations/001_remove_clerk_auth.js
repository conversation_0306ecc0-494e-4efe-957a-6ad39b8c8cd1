import mongoose from "mongoose";
import User from "../models/user.model.js";
import Transaction from "../models/transaction.model.js";
import bcrypt from "bcryptjs";

/**
 * Migration script to remove Clerk authentication and update to custom auth
 * This script will:
 * 1. Remove Clerk-related fields from users
 * 2. Simplify user subscription fields
 * 3. Simplify transaction model fields
 * 4. Set default passwords for existing users (they'll need to reset)
 */

export const migrateFromClerkAuth = async () => {
  try {
    console.log("Starting migration from Clerk authentication...");

    // Connect to database if not already connected
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(process.env.MONGODB_URI);
    }

    // Step 1: Update User collection
    console.log("Step 1: Updating User collection...");
    
    // Get all users
    const users = await User.find({});
    console.log(`Found ${users.length} users to migrate`);

    for (const user of users) {
      const updates = {};

      // Remove Clerk-related fields
      if (user.clerkId) {
        updates.$unset = { 
          clerkId: 1,
          // Remove other deprecated fields
          subscriptionTier: 1,
          subscriptionStatus: 1,
          subscriptionExpiry: 1,
          currentPlanDetails: 1,
          autoRenew: 1,
          nextRenewalDate: 1,
          cancellation: 1,
          trial: 1,
          accessLevel: 1,
          lastTransaction: 1,
          subscriptionMetadata: 1,
          createdBy: 1
        };
      }

      // Set provider based on existing data
      if (user.googleId && !user.provider) {
        updates.provider = "google";
        updates.isEmailVerified = true; // Google users are pre-verified
      } else if (!user.provider) {
        updates.provider = "email";
      }

      // Generate username if missing
      if (!user.username) {
        const baseUsername = user.email.split('@')[0];
        let username = baseUsername;
        let counter = 1;
        
        // Ensure username is unique
        while (await User.findOne({ username })) {
          username = `${baseUsername}_${counter}`;
          counter++;
        }
        updates.username = username;
      }

      // Set default password for email users without password
      if (user.provider === "email" && !user.password) {
        // Generate a random password that user will need to reset
        const tempPassword = Math.random().toString(36).slice(-8);
        const hashedPassword = await bcrypt.hash(tempPassword, 10);
        updates.password = hashedPassword;
        updates.passwordResetToken = "MIGRATION_RESET_REQUIRED";
        console.log(`User ${user.email} needs password reset - temp password: ${tempPassword}`);
      }

      // Apply updates if any
      if (Object.keys(updates).length > 0) {
        await User.updateOne({ _id: user._id }, updates);
        console.log(`Updated user: ${user.email}`);
      }
    }

    // Step 2: Update Transaction collection
    console.log("Step 2: Updating Transaction collection...");
    
    const transactions = await Transaction.find({});
    console.log(`Found ${transactions.length} transactions to migrate`);

    for (const transaction of transactions) {
      const updates = {};
      const metadata = transaction.metadata || {};

      // Move complex nested data to metadata
      if (transaction.userSubscriptionData) {
        metadata.userSubscriptionData = transaction.userSubscriptionData;
        updates.$unset = { userSubscriptionData: 1 };
      }

      if (transaction.planDetails) {
        metadata.planSnapshot = transaction.planDetails;
        updates.$unset = { ...updates.$unset, planDetails: 1 };
      }

      if (transaction.couponDetails) {
        metadata.couponSnapshot = transaction.couponDetails;
        updates.$unset = { ...updates.$unset, couponDetails: 1 };
      }

      if (transaction.billingDetails) {
        metadata.billingDetails = transaction.billingDetails;
        updates.$unset = { ...updates.$unset, billingDetails: 1 };
      }

      // Update metadata
      if (Object.keys(metadata).length > 0) {
        updates.metadata = metadata;
      }

      // Apply updates if any
      if (Object.keys(updates).length > 0) {
        await Transaction.updateOne({ _id: transaction._id }, updates);
        console.log(`Updated transaction: ${transaction._id}`);
      }
    }

    // Step 3: Create indexes for new fields
    console.log("Step 3: Creating indexes...");
    
    // Ensure username index exists
    try {
      await User.collection.createIndex({ username: 1 }, { unique: true });
      console.log("Created username index");
    } catch (error) {
      console.log("Username index already exists or error:", error.message);
    }

    // Ensure googleId index exists with sparse option
    try {
      await User.collection.createIndex({ googleId: 1 }, { unique: true, sparse: true });
      console.log("Created googleId index");
    } catch (error) {
      console.log("GoogleId index already exists or error:", error.message);
    }

    console.log("Migration completed successfully!");
    
    return {
      success: true,
      usersUpdated: users.length,
      transactionsUpdated: transactions.length
    };

  } catch (error) {
    console.error("Migration failed:", error);
    throw error;
  }
};

// Rollback function (use with caution)
export const rollbackClerkMigration = async () => {
  console.log("WARNING: This will attempt to rollback the migration");
  console.log("This is not recommended and may cause data loss");
  
  // This is a basic rollback - in production you'd want more sophisticated rollback logic
  // For now, we'll just log what would be done
  console.log("Rollback would:");
  console.log("1. Remove username field from users without clerkId");
  console.log("2. Move metadata back to separate fields in transactions");
  console.log("3. Remove custom auth fields");
  
  throw new Error("Rollback not implemented - manual intervention required");
};

// CLI runner
if (process.argv[2] === 'migrate') {
  migrateFromClerkAuth()
    .then((result) => {
      console.log("Migration result:", result);
      process.exit(0);
    })
    .catch((error) => {
      console.error("Migration error:", error);
      process.exit(1);
    });
}

if (process.argv[2] === 'rollback') {
  rollbackClerkMigration()
    .catch((error) => {
      console.error("Rollback error:", error);
      process.exit(1);
    });
}
