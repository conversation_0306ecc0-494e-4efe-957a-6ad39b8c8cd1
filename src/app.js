// app.js
import express from "express";
import authRoutes from "./routes/auth.routes.js";
import sessionRoutes from "./routes/session.routes.js";
import userRoutes from "./routes/user.routes.js";
import profileRoutes from "./routes/profile.routes.js";
import planRoutes from "./routes/plan.routes.js";

import couponRoutes from "./routes/coupon.routes.js";
import transactionRoutes from "./routes/transaction.routes.js";
import productRoutes from "./routes/product.routes.js";
import galleryRoutes from "./routes/gallery.routes.js";
import folderRoutes from "./routes/folder.routes.js";
import fileRoutes from "./routes/file.routes.js";
import webhookRoutes from "./routes/webhook.routes.js";
import healthRoutes from "./routes/health.routes.js";
import systemRoutes from "./routes/system.routes.js";

import { swaggerSpec, swaggerUi } from "./utils/swagger.js";
import { errorMiddleware } from "./middlewares/error.middleware.js";
import { protect } from "./middlewares/auth.middleware.js";
import { globalRateLimit } from "./middlewares/rateLimiting.middleware.js";
import cors from "cors";
import cookieParser from "cookie-parser";
import passport from "./config/passport.js";
import { initializeWebhookHandlers, validateWebhookConfiguration } from "./services/webhooks/index.js";
import { SessionCleanupService } from "./services/cleanup/SessionCleanupService.js";

const app = express();

// Initialize services
initializeWebhookHandlers();
validateWebhookConfiguration();

// Initialize session cleanup service (only in production/development, not in tests)
if (process.env.NODE_ENV !== 'test') {
  SessionCleanupService.initialize();
}

app.use(cookieParser());
app.use(passport.initialize());

// CORS setup
if (process.env.NODE_ENV === "production") {
  console.log("production");  
  app.use(
    cors({
      origin: [process.env.CLIENT_URL, "https://designbyte.shop"],
      credentials: true,
    })
  );
} else {
  console.log("cors development");
  app.use(cors({ origin: true, credentials: true }));
}

// Apply global rate limiting to all routes except auth (auth has its own stricter rate limiting)
app.use((req, res, next) => {
  if (req.path.startsWith("/api/v1/auth")) {
    return next();
  }
  globalRateLimit(req, res, next);
});

app.use(express.json());

// Routes
app.use("/api/v1/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));
app.use("/api/v1/health", healthRoutes);
app.use("/api/v1/system", systemRoutes);
app.use("/api/v1/users", protect, userRoutes);
app.use("/api/v1/profile", profileRoutes); // Profile routes have their own auth protection
app.use("/api/v1/auth", authRoutes); // Auth routes have their own stricter rate limiting
app.use("/api/v1/sessions", sessionRoutes); // Session management routes
app.use("/api/v1/plans", planRoutes);

app.use("/api/v1/coupons", couponRoutes);
app.use("/api/v1/transactions", protect, transactionRoutes);
app.use("/api/v1/products", productRoutes);
app.use("/api/v1/files", galleryRoutes);
app.use("/api/v1/folders", folderRoutes);
app.use("/api/v1/file", fileRoutes);
app.use("/api/v1/webhooks", webhookRoutes);

// Move errorMiddleware to the end
app.use(errorMiddleware);

export default app;
