// Email verification middleware
export const requireEmailVerification = (req, res, next) => {
  try {
    // Check if user exists (should be set by protect middleware)
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required"
      });
    }

    // Check if email is verified
    if (!req.user.isEmailVerified) {
      return res.status(403).json({
        success: false,
        message: "Email verification required. Please check your email and verify your account.",
        requiresEmailVerification: true,
        userEmail: req.user.email
      });
    }

    next();
  } catch (error) {
    console.error("Email verification middleware error:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Optional email verification middleware (warns but doesn't block)
export const warnEmailVerification = (req, res, next) => {
  try {
    if (req.user && !req.user.isEmailVerified) {
      // Add warning to response headers
      res.set('X-Email-Verification-Warning', 'Email not verified');
    }
    next();
  } catch (error) {
    console.error("Email verification warning middleware error:", error);
    next(); // Continue even if there's an error
  }
};
