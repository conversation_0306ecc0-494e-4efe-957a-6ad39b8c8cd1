import rateLimit from 'express-rate-limit';
import { webhookRegistry } from '../services/webhooks/WebhookHandler.js';

/**
 * Webhook Rate Limiting
 * Protect against webhook spam/abuse
 */
export const webhookRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: 'Too many webhook requests, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip rate limiting for successful webhooks from known IPs
  skip: (req) => {
    // Add your payment gateway IPs here
    const trustedIPs = [
      // Razorpay IPs (add actual IPs)
      '************',
      '************',
      // Add other gateway IPs
    ];
    
    const clientIP = req.ip || req.connection.remoteAddress;
    return trustedIPs.includes(clientIP);
  },
});

/**
 * Raw Body Parser for Webhooks
 * Some payment gateways require raw body for signature verification
 */
export const rawBodyParser = (req, res, next) => {
  if (req.headers['content-type'] === 'application/json') {
    let data = '';
    req.setEncoding('utf8');
    
    req.on('data', (chunk) => {
      data += chunk;
    });
    
    req.on('end', () => {
      try {
        req.rawBody = data;
        req.body = JSON.parse(data);
        next();
      } catch (error) {
        return res.status(400).json({
          success: false,
          error: 'Invalid JSON payload',
        });
      }
    });
  } else {
    next();
  }
};

/**
 * Webhook Validation Middleware
 * Validates webhook payload and signature
 */
export const validateWebhook = (gatewayName) => {
  return async (req, res, next) => {
    try {
      const gateway = gatewayName.toLowerCase();
      const handler = webhookRegistry.getHandler(gateway);
      
      if (!handler) {
        return res.status(400).json({
          success: false,
          error: `Unsupported payment gateway: ${gatewayName}`,
        });
      }

      // Get signature from headers (different for each gateway)
      let signature;
      switch (gateway) {
        case 'razorpay':
          signature = req.headers['x-razorpay-signature'];
          break;
        case 'stripe':
          signature = req.headers['stripe-signature'];
          break;
        case 'lemonsqueezy':
          signature = req.headers['x-signature'];
          break;
        case 'paddle':
          signature = req.headers['paddle-signature'];
          break;
        default:
          signature = req.headers['x-webhook-signature'];
      }

      if (!signature) {
        return res.status(400).json({
          success: false,
          error: 'Missing webhook signature',
        });
      }

      // Verify signature
      const secret = handler.getWebhookSecret();
      if (!secret) {
        console.error(`No webhook secret configured for ${gateway}`);
        return res.status(500).json({
          success: false,
          error: 'Webhook secret not configured',
        });
      }

      const isValid = handler.verifySignature(
        req.rawBody || req.body,
        signature,
        secret
      );

      if (!isValid) {
        console.error(`Invalid webhook signature for ${gateway}`);
        return res.status(401).json({
          success: false,
          error: 'Invalid webhook signature',
        });
      }

      // Add validated data to request
      req.webhookGateway = gateway;
      req.webhookSignature = signature;
      req.webhookHandler = handler;
      
      next();
    } catch (error) {
      console.error('Webhook validation error:', error);
      return res.status(500).json({
        success: false,
        error: 'Webhook validation failed',
      });
    }
  };
};

/**
 * Webhook Logging Middleware
 * Log all webhook requests for debugging and monitoring
 */
export const logWebhook = (req, res, next) => {
  const startTime = Date.now();
  
  // Log incoming webhook
  console.log(`[WEBHOOK] ${req.method} ${req.path}`, {
    gateway: req.webhookGateway,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    contentType: req.headers['content-type'],
    timestamp: new Date().toISOString(),
  });

  // Log response
  const originalSend = res.send;
  res.send = function(data) {
    const duration = Date.now() - startTime;
    console.log(`[WEBHOOK] Response ${res.statusCode}`, {
      gateway: req.webhookGateway,
      duration: `${duration}ms`,
      success: res.statusCode < 400,
    });
    
    originalSend.call(this, data);
  };

  next();
};

/**
 * Webhook Error Handler
 * Handle webhook-specific errors
 */
export const webhookErrorHandler = (error, req, res, next) => {
  console.error('[WEBHOOK ERROR]', {
    gateway: req.webhookGateway,
    error: error.message,
    stack: error.stack,
    body: req.body,
  });

  // Don't expose internal errors to webhook senders
  const statusCode = error.statusCode || 500;
  const message = statusCode < 500 ? error.message : 'Internal server error';

  res.status(statusCode).json({
    success: false,
    error: message,
    gateway: req.webhookGateway,
  });
};

/**
 * Webhook Retry Handler
 * Handle webhook processing with retry logic
 */
export class WebhookRetryHandler {
  constructor(maxRetries = 3, retryDelay = 1000) {
    this.maxRetries = maxRetries;
    this.retryDelay = retryDelay;
  }

  /**
   * Process webhook with retry logic
   * @param {Function} processor - Webhook processing function
   * @param {Object} data - Webhook data
   * @param {number} attempt - Current attempt number
   * @returns {Object} - Processing result
   */
  async processWithRetry(processor, data, attempt = 1) {
    try {
      return await processor(data);
    } catch (error) {
      console.error(`Webhook processing attempt ${attempt} failed:`, error.message);
      
      if (attempt < this.maxRetries) {
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        return this.processWithRetry(processor, data, attempt + 1);
      }
      
      // All retries exhausted
      throw new Error(`Webhook processing failed after ${this.maxRetries} attempts: ${error.message}`);
    }
  }
}

/**
 * Webhook Idempotency Handler
 * Prevent duplicate webhook processing
 */
export class WebhookIdempotencyHandler {
  constructor() {
    this.processedWebhooks = new Map();
    this.cleanupInterval = 60 * 60 * 1000; // 1 hour
    
    // Cleanup old entries periodically
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  /**
   * Check if webhook has already been processed
   * @param {string} webhookId - Unique webhook identifier
   * @returns {boolean} - Whether webhook was already processed
   */
  isProcessed(webhookId) {
    return this.processedWebhooks.has(webhookId);
  }

  /**
   * Mark webhook as processed
   * @param {string} webhookId - Unique webhook identifier
   * @param {Object} result - Processing result
   */
  markProcessed(webhookId, result) {
    this.processedWebhooks.set(webhookId, {
      result,
      timestamp: Date.now(),
    });
  }

  /**
   * Get previous processing result
   * @param {string} webhookId - Unique webhook identifier
   * @returns {Object} - Previous result
   */
  getPreviousResult(webhookId) {
    const entry = this.processedWebhooks.get(webhookId);
    return entry ? entry.result : null;
  }

  /**
   * Cleanup old processed webhooks
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    for (const [webhookId, entry] of this.processedWebhooks.entries()) {
      if (now - entry.timestamp > maxAge) {
        this.processedWebhooks.delete(webhookId);
      }
    }
  }

  /**
   * Generate webhook ID from payload
   * @param {string} gateway - Payment gateway name
   * @param {Object} payload - Webhook payload
   * @returns {string} - Unique webhook ID
   */
  generateWebhookId(gateway, payload) {
    // Different gateways have different unique identifiers
    let uniqueField;
    
    switch (gateway) {
      case 'razorpay':
        uniqueField = payload.payload?.payment?.id || 
                     payload.payload?.subscription?.id || 
                     payload.payload?.invoice?.id;
        break;
      case 'stripe':
        uniqueField = payload.id;
        break;
      case 'lemonsqueezy':
        uniqueField = payload.meta?.event_name + '_' + payload.data?.id;
        break;
      default:
        uniqueField = JSON.stringify(payload);
    }
    
    return `${gateway}_${uniqueField}_${payload.event || payload.type}`;
  }
}

// Create singleton instances
export const webhookRetryHandler = new WebhookRetryHandler();
export const webhookIdempotencyHandler = new WebhookIdempotencyHandler();
