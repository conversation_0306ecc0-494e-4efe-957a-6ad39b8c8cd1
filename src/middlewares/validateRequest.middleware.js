import { validationResult } from "express-validator";

/**
 * Middleware to handle validation errors from express-validator.
 * If there are validation errors, it responds with a 400 status code and the errors.
 * Otherwise, it proceeds to the next middleware or route handler.
 */
export const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array().map((error) => ({
        field: error.param,
        message: error.msg,
      })),
    });
  }
  next();
};
