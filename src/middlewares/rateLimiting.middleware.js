import { sessionService } from "../config/redis.js";
import { asyncHandler } from "../utils/asyncHandler.js";

// Global rate limiting (for general API usage)
export const globalRateLimit = asyncHandler(async (req, res, next) => {
  const identifier = req.ip;
  const result = await sessionService.checkRateLimit(
    `global:${identifier}`, 
    100, // 100 requests
    3600 // per hour
  );

  if (!result.allowed) {
    return res.status(429).json({
      success: false,
      error: "Too many requests. Please try again later.",
      resetTime: result.resetTime
    });
  }

  // Add rate limit info to headers
  res.set({
    'X-RateLimit-Limit': '100',
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
  });

  next();
});

// Auth rate limiting (stricter for login/register)
export const authRateLimit = asyncHandler(async (req, res, next) => {
  const identifier = req.ip;
  const result = await sessionService.checkRateLimit(
    `auth:${identifier}`, 
    5, // 5 attempts
    900 // per 15 minutes
  );

  if (!result.allowed) {
    return res.status(429).json({
      success: false,
      error: "Too many authentication attempts. Please try again in 15 minutes.",
      resetTime: result.resetTime
    });
  }

  res.set({
    'X-RateLimit-Limit': '5',
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
  });

  next();
});

