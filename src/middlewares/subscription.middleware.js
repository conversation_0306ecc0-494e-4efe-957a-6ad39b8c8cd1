import User from "../models/user.model.js";
import Product from "../models/product.model.js";

/**
 * Middleware to check if user has valid subscription
 */
export const requireSubscription = (requiredTiers = []) => {
  return async (req, res, next) => {
    try {
      const { userId } = req.auth;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Authentication required",
        });
      }

      const user = await User.findOne({ clerkId: userId });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        });
      }

      // Check if user has active subscription
      if (!user.hasActiveSubscription()) {
        return res.status(403).json({
          success: false,
          message: "Active subscription required",
          subscription: {
            isPro: user.isPro,
            isLifetime: user.isLifetimePro,
            endDate: user.subscriptionEndDate,
          },
        });
      }

      // Check if user's subscription tier meets requirements (if specified)
      if (requiredTiers.length > 0) {
        const userTier = user.isLifetimePro ? "lifetime" :
                        user.currentPlan?.duration || "free";

        if (!requiredTiers.includes(userTier)) {
          return res.status(403).json({
            success: false,
            message: "Higher subscription tier required",
            currentTier: userTier,
            requiredTiers,
          });
        }
      }

      req.user = user;
      next();
    } catch (error) {
      console.error("Subscription middleware error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  };
};

/**
 * Middleware to check product access permissions
 */
export const checkProductAccess = async (req, res, next) => {
  try {
    const { userId } = req.auth;
    const { productId, slug } = req.params;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    const user = await User.findOne({ clerkId: userId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Find product by ID or slug
    let product;
    if (productId) {
      product = await Product.findById(productId);
    } else if (slug) {
      product = await Product.findOne({ slug, status: "active" });
    }

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    // Check if product is free
    if (!product.isPaid || product.accessLevel === "free") {
      req.user = user;
      req.product = product;
      req.hasAccess = true;
      return next();
    }

    // Check user access to paid product
    const hasAccess = user.hasAccessToProduct(product);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: "Access denied. Subscription required.",
        product: {
          name: product.name,
          accessLevel: product.accessLevel,
          requiredSubscription: product.requiredSubscription,
        },
        userSubscription: {
          isPro: user.isPro,
          isLifetime: user.isLifetimePro,
          hasActive: user.hasActiveSubscription(),
          endDate: user.subscriptionEndDate,
        },
      });
    }

    req.user = user;
    req.product = product;
    req.hasAccess = true;
    next();
  } catch (error) {
    console.error("Product access middleware error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};



/**
 * Middleware to validate subscription status and sync with database
 */
export const syncSubscriptionStatus = async (req, _res, next) => {
  try {
    const { userId } = req.auth;

    if (!userId) {
      return next();
    }

    const user = await User.findOne({ clerkId: userId }).populate('currentPlan');
    if (!user) {
      return next();
    }

    // Check if user's subscription is expired (only for non-lifetime users)
    if (!user.isLifetimePro && user.subscriptionEndDate && user.subscriptionEndDate < new Date()) {
      // Mark subscription as expired
      user.isPro = false;
      user.subscriptionEndDate = null;
      user.currentPlan = null;
      await user.save();
    }

    req.user = user;
    next();
  } catch (error) {
    console.error("Sync subscription middleware error:", error);
    next(); // Continue even if sync fails
  }
};

export default {
  requireSubscription,
  checkProductAccess,
  syncSubscriptionStatus,
};
