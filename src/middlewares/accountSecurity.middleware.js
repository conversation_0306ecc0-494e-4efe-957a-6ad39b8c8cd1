import { AccountSecurityService } from "../services/security/AccountSecurityService.js";
import { DigitalGoodsSecurityService } from "../services/security/DigitalGoodsSecurityService.js";
import User from "../models/user.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import logger from "../utils/logger.js";

/**
 * Middleware to check if user account is suspended
 */
export const checkAccountSuspension = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required"
      });
    }

    // Check if user is currently suspended
    if (req.user.isCurrentlySuspended()) {
      const suspensionInfo = {
        reason: req.user.suspensionReason,
        suspendedAt: req.user.suspendedAt,
        expiresAt: req.user.suspensionExpiresAt
      };

      logger.warn(`Suspended user attempted access: ${req.user._id}`, suspensionInfo);

      return res.status(403).json({
        success: false,
        message: "Account is suspended",
        suspensionInfo,
        code: "ACCOUNT_SUSPENDED"
      });
    }

    // Check if account is locked due to failed login attempts
    if (req.user.isAccountLocked()) {
      return res.status(423).json({
        success: false,
        message: "Account is temporarily locked due to multiple failed login attempts",
        lockedUntil: req.user.loginAttempts.lockedUntil,
        code: "ACCOUNT_LOCKED"
      });
    }

    next();
  } catch (error) {
    logger.error("Error in account suspension check:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
});

/**
 * Middleware to track user sessions and detect account sharing
 */
export const trackUserSession = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user || !req.headers.authorization) {
      return next();
    }

    const sessionId = req.headers['x-session-id'];
    if (!sessionId) {
      return next(); // Skip if no session ID provided
    }

    // Validate session
    const sessionValidation = await AccountSecurityService.validateSession(
      sessionId, 
      req.originalUrl
    );

    if (!sessionValidation.valid) {
      return res.status(401).json({
        success: false,
        message: "Invalid or expired session",
        reason: sessionValidation.reason,
        code: "SESSION_INVALID"
      });
    }

    // Attach session info to request
    req.session = sessionValidation.session;
    
    next();
  } catch (error) {
    logger.error("Error in session tracking:", error);
    next(); // Continue without session tracking on error
  }
});

/**
 * Middleware to detect and prevent account sharing
 */
export const detectAccountSharing = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    // Skip check for certain endpoints to avoid performance issues
    const skipEndpoints = ['/api/v1/health', '/api/v1/auth/logout'];
    if (skipEndpoints.some(endpoint => req.path.startsWith(endpoint))) {
      return next();
    }

    // Perform account sharing detection (async, don't block request)
    AccountSecurityService.detectAccountSharing(req.user._id)
      .then(result => {
        if (result.sharing && result.confidence > 0.8) {
          // High confidence account sharing detected
          logger.warn(`High confidence account sharing detected for user ${req.user._id}`, {
            confidence: result.confidence,
            indicators: result.indicators,
            sessionCount: result.sessions.length
          });

          // Flag user account
          req.user.flagAccountSharing().catch(err => {
            logger.error("Error flagging account sharing:", err);
          });

          // Optionally suspend account for severe violations
          if (result.confidence > 0.9 && result.sessions.length > 5) {
            req.user.suspendAccount(
              "Automatic suspension due to account sharing violation",
              null, // System suspension
              new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
              `Confidence: ${result.confidence}, Sessions: ${result.sessions.length}`
            ).catch(err => {
              logger.error("Error suspending account:", err);
            });
          }
        }
      })
      .catch(error => {
        logger.error("Error in account sharing detection:", error);
      });

    next();
  } catch (error) {
    logger.error("Error in account sharing detection middleware:", error);
    next(); // Continue without detection on error
  }
});

/**
 * Middleware to enforce single session policy for specific user tiers
 */
export const enforceSingleSession = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    // Only enforce for free tier users or specific plan types
    const shouldEnforce = req.user.currentPlan && 
      (req.user.currentPlan.planType === 'free' || 
       req.user.currentPlan.tier === 'free');

    if (!shouldEnforce) {
      return next();
    }

    const sessionId = req.headers['x-session-id'];
    if (!sessionId) {
      return next();
    }

    // Check for multiple active sessions
    const isMultipleSessions = await AccountSecurityService.detectMultipleSessions(
      req.user._id, 
      1 // Single session limit for free tier
    );

    if (isMultipleSessions) {
      // Terminate all other sessions except current one
      await AccountSecurityService.terminateAllUserSessions(
        req.user._id, 
        "single_session_policy_enforced"
      );

      logger.info(`Single session policy enforced for user ${req.user._id}`);
    }

    next();
  } catch (error) {
    logger.error("Error in single session enforcement:", error);
    next(); // Continue without enforcement on error
  }
});

/**
 * Middleware to log security events
 */
export const logSecurityEvent = (eventType) => {
  return asyncHandler(async (req, res, next) => {
    try {
      const securityEvent = {
        eventType,
        userId: req.user?._id,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        endpoint: req.originalUrl,
        method: req.method,
        timestamp: new Date(),
        sessionId: req.headers['x-session-id']
      };

      // Log to security audit trail
      logger.info(`Security Event: ${eventType}`, securityEvent);

      // Store in database for audit purposes (optional)
      // await SecurityAuditLog.create(securityEvent);

      next();
    } catch (error) {
      logger.error("Error logging security event:", error);
      next(); // Continue without logging on error
    }
  });
};

/**
 * Middleware to check user permissions for digital goods access
 */
export const checkDigitalGoodsAccess = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required for digital goods access"
      });
    }

    // Check if user has active subscription for paid content
    const hasAccess = req.user.hasActiveSubscription();
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: "Active subscription required for digital goods access",
        code: "SUBSCRIPTION_REQUIRED"
      });
    }

    // Check if account is flagged for sharing
    if (req.user.securityFlags.accountSharingDetected) {
      return res.status(403).json({
        success: false,
        message: "Access restricted due to account sharing violation",
        code: "ACCOUNT_SHARING_VIOLATION"
      });
    }

    next();
  } catch (error) {
    logger.error("Error checking digital goods access:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
});

/**
 * Rate limiting for sensitive operations
 */
export const sensitiveOperationRateLimit = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    const key = `sensitive_ops:${req.user._id}`;
    const limit = 10; // 10 operations per hour
    const window = 3600; // 1 hour

    const { sessionService } = await import("../config/redis.js");
    const result = await sessionService.checkRateLimit(key, limit, window);

    if (!result.allowed) {
      logger.warn(`Sensitive operation rate limit exceeded for user ${req.user._id}`);

      return res.status(429).json({
        success: false,
        message: "Too many sensitive operations. Please try again later.",
        resetTime: result.resetTime,
        code: "RATE_LIMIT_EXCEEDED"
      });
    }

    next();
  } catch (error) {
    logger.error("Error in sensitive operation rate limiting:", error);
    next(); // Continue without rate limiting on error
  }
});

/**
 * Middleware to validate digital goods download access
 */
export const validateDigitalGoodsDownload = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required for digital goods access"
      });
    }

    // Get product from request (should be set by previous middleware)
    const product = req.product;
    if (!product) {
      return res.status(400).json({
        success: false,
        message: "Product information required"
      });
    }

    // Validate access
    const validation = await DigitalGoodsSecurityService.validateDigitalGoodsAccess(
      req.user,
      product,
      req
    );

    if (!validation.allowed) {
      return res.status(403).json({
        success: false,
        message: "Access denied",
        restrictions: validation.restrictions,
        code: "DIGITAL_GOODS_ACCESS_DENIED"
      });
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      logger.warn(`Digital goods access warnings for user ${req.user._id}`, {
        warnings: validation.warnings,
        securityFlags: validation.securityFlags
      });
    }

    // Attach validation result to request
    req.digitalGoodsValidation = validation;

    next();
  } catch (error) {
    logger.error("Error validating digital goods download:", error);
    return res.status(500).json({
      success: false,
      message: "Security validation failed"
    });
  }
});

/**
 * Middleware to generate secure download token
 */
export const generateDownloadToken = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user || !req.product) {
      return res.status(400).json({
        success: false,
        message: "User and product information required"
      });
    }

    const tokenData = await DigitalGoodsSecurityService.generateSecureDownloadToken(
      req.user,
      req.product,
      req
    );

    // Attach token data to request
    req.downloadToken = tokenData;

    next();
  } catch (error) {
    logger.error("Error generating download token:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to generate download token"
    });
  }
});

/**
 * Middleware to validate download token
 */
export const validateDownloadToken = asyncHandler(async (req, res, next) => {
  try {
    const token = req.params.token || req.query.token || req.headers['x-download-token'];

    if (!token) {
      return res.status(400).json({
        success: false,
        message: "Download token required"
      });
    }

    const validation = await DigitalGoodsSecurityService.validateDownloadToken(token, req);

    if (!validation.valid) {
      return res.status(401).json({
        success: false,
        message: "Invalid download token",
        reason: validation.reason,
        code: "INVALID_DOWNLOAD_TOKEN"
      });
    }

    // Attach token data to request
    req.tokenData = validation.tokenData;

    next();
  } catch (error) {
    logger.error("Error validating download token:", error);
    return res.status(500).json({
      success: false,
      message: "Token validation failed"
    });
  }
});

/**
 * Middleware to track download activity
 */
export const trackDownloadActivity = (action = 'download') => {
  return asyncHandler(async (req, res, next) => {
    try {
      if (req.user && req.product) {
        // Log the download access
        await DigitalGoodsSecurityService.logDownloadAccess(
          req.user,
          req.product,
          req,
          action
        );

        // Generate unique download ID for concurrent tracking
        const downloadId = `${req.user._id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        req.downloadId = downloadId;

        // Track download start
        await DigitalGoodsSecurityService.trackDownloadStart(req.user._id, downloadId);

        // Set up cleanup on response finish
        res.on('finish', async () => {
          try {
            await DigitalGoodsSecurityService.trackDownloadEnd(req.user._id, downloadId);
          } catch (error) {
            logger.error("Error tracking download end:", error);
          }
        });
      }

      next();
    } catch (error) {
      logger.error("Error tracking download activity:", error);
      next(); // Continue without tracking on error
    }
  });
};
