import { body, param, query } from "express-validator";
import { validateRequest } from "./validateRequest.middleware.js";

/**
 * Input Sanitization Middleware
 * Sanitizes and validates common input fields to prevent XSS and injection attacks
 */

// Common sanitization rules
const sanitizeString = (field) => 
  body(field)
    .optional()
    .trim()
    .escape() // Escapes HTML characters
    .isLength({ max: 1000 })
    .withMessage(`${field} must be less than 1000 characters`);

const sanitizeEmail = (field) =>
  body(field)
    .optional()
    .trim()
    .normalizeEmail()
    .isEmail()
    .withMessage(`${field} must be a valid email address`);

const sanitizeObjectId = (field) =>
  body(field)
    .optional()
    .trim()
    .isMongoId()
    .withMessage(`${field} must be a valid ID`);

const sanitizeNumber = (field, min = 0, max = Number.MAX_SAFE_INTEGER) =>
  body(field)
    .optional()
    .isNumeric()
    .toFloat()
    .isFloat({ min, max })
    .withMessage(`${field} must be a number between ${min} and ${max}`);

const sanitizeBoolean = (field) =>
  body(field)
    .optional()
    .isBoolean()
    .toBoolean()
    .withMessage(`${field} must be a boolean value`);

// User input sanitization
export const sanitizeUserInput = [
  sanitizeEmail("email"),
  sanitizeString("username"),
  sanitizeString("firstName"),
  sanitizeString("lastName"),
  sanitizeString("phoneNumber"),
  body("password")
    .optional()
    .isLength({ min: 6, max: 128 })
    .withMessage("Password must be between 6 and 128 characters"),
  sanitizeString("profileImage"),
  body("role")
    .optional()
    .isIn(["user", "admin", "super_admin"])
    .withMessage("Invalid role specified"),
  sanitizeBoolean("isDeleted"),
  sanitizeBoolean("isEmailVerified"),
  sanitizeBoolean("isPro"),
  sanitizeBoolean("isLifetimePro"),
  sanitizeObjectId("currentPlan"),
  validateRequest
];

// Plan input sanitization
export const sanitizePlanInput = [
  body("name")
    .trim()
    .escape()
    .isLength({ min: 2, max: 100 })
    .withMessage("Plan name must be between 2 and 100 characters"),
  sanitizeString("description"),
  sanitizeNumber("price", 0, 999999),
  body("currency")
    .optional()
    .trim()
    .isIn(["USD", "EUR", "GBP", "INR"])
    .withMessage("Invalid currency"),
  body("duration")
    .optional()
    .isIn(["monthly", "yearly", "lifetime"])
    .withMessage("Invalid duration"),
  sanitizeString("priceId"),
  body("planType")
    .optional()
    .isIn(["free", "individual", "startup", "enterprise"])
    .withMessage("Invalid plan type"),
  body("tier")
    .optional()
    .isIn(["free", "basic", "pro", "premium", "enterprise"])
    .withMessage("Invalid tier"),
  sanitizeBoolean("isActive"),
  sanitizeBoolean("isVisible"),
  sanitizeBoolean("isFeatured"),
  sanitizeNumber("sortOrder", 0, 1000),
  validateRequest
];

// Product input sanitization
export const sanitizeProductInput = [
  body("name")
    .trim()
    .escape()
    .isLength({ min: 2, max: 200 })
    .withMessage("Product name must be between 2 and 200 characters"),
  sanitizeString("description"),
  sanitizeString("shortDescription"),
  sanitizeNumber("price", 0, 999999),
  body("category")
    .optional()
    .trim()
    .isIn(["figma-kits", "themes", "starter-kits"])
    .withMessage("Invalid category"),
  sanitizeString("techStack"),
  sanitizeString("downloadUrl"),
  sanitizeString("previewUrl"),
  sanitizeString("thumbnailUrl"),
  sanitizeBoolean("isPaid"),
  sanitizeBoolean("isActive"),
  sanitizeBoolean("isFeatured"),
  sanitizeNumber("sortOrder", 0, 1000),
  validateRequest
];

// Transaction input sanitization
export const sanitizeTransactionInput = [
  sanitizeObjectId("user"),
  sanitizeObjectId("plan"),
  sanitizeObjectId("coupon"),
  sanitizeNumber("amount", 0, 999999),
  sanitizeNumber("originalAmount", 0, 999999),
  sanitizeNumber("discountAmount", 0, 999999),
  body("currency")
    .optional()
    .trim()
    .isIn(["USD", "EUR", "GBP", "INR"])
    .withMessage("Invalid currency"),
  body("status")
    .optional()
    .isIn(["pending", "completed", "failed", "refunded", "cancelled"])
    .withMessage("Invalid status"),
  body("paymentGateway")
    .optional()
    .isIn(["stripe", "razorpay", "paypal", "manual"])
    .withMessage("Invalid payment gateway"),
  sanitizeString("gatewayTransactionId"),
  sanitizeString("gatewaySubscriptionId"),
  sanitizeString("description"),
  sanitizeString("internalNotes"),
  validateRequest
];

// Coupon input sanitization
export const sanitizeCouponInput = [
  body("code")
    .trim()
    .toUpperCase()
    .isLength({ min: 3, max: 20 })
    .matches(/^[A-Z0-9_-]+$/)
    .withMessage("Coupon code must be 3-20 characters, uppercase letters, numbers, underscore, or dash only"),
  body("discountType")
    .isIn(["percentage", "fixed"])
    .withMessage("Discount type must be 'percentage' or 'fixed'"),
  sanitizeNumber("discountValue", 0.01, 999999),
  sanitizeNumber("maxRedemptions", 1, 999999),
  sanitizeNumber("currentRedemptions", 0, 999999),
  body("validFrom")
    .isISO8601()
    .toDate()
    .withMessage("Valid from must be a valid date"),
  body("validUntil")
    .isISO8601()
    .toDate()
    .withMessage("Valid until must be a valid date"),
  sanitizeBoolean("isActive"),
  validateRequest
];

// Query parameter sanitization
export const sanitizeQueryParams = [
  query("page")
    .optional()
    .isInt({ min: 1, max: 1000 })
    .toInt()
    .withMessage("Page must be a positive integer"),
  query("limit")
    .optional()
    .isInt({ min: 1, max: 100 })
    .toInt()
    .withMessage("Limit must be between 1 and 100"),
  query("search")
    .optional()
    .trim()
    .escape()
    .isLength({ max: 100 })
    .withMessage("Search query must be less than 100 characters"),
  query("sort")
    .optional()
    .trim()
    .matches(/^[a-zA-Z_-]+$/)
    .withMessage("Sort field contains invalid characters"),
  query("order")
    .optional()
    .isIn(["asc", "desc"])
    .withMessage("Order must be 'asc' or 'desc'"),
  validateRequest
];

// URL parameter sanitization
export const sanitizeUrlParams = [
  param("id")
    .isMongoId()
    .withMessage("Invalid ID format"),
  validateRequest
];

// General text sanitization for rich content
export const sanitizeRichText = (field) => [
  body(field)
    .optional()
    .trim()
    .isLength({ max: 10000 })
    .withMessage(`${field} must be less than 10000 characters`)
    .customSanitizer((value) => {
      // Allow basic HTML tags but escape dangerous ones
      return value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    }),
  validateRequest
];

// File upload sanitization
export const sanitizeFileUpload = [
  body("fileName")
    .optional()
    .trim()
    .matches(/^[a-zA-Z0-9._-]+$/)
    .isLength({ max: 255 })
    .withMessage("Invalid file name"),
  body("fileSize")
    .optional()
    .isInt({ min: 1, max: 50 * 1024 * 1024 }) // 50MB max
    .withMessage("File size must be between 1 byte and 50MB"),
  body("mimeType")
    .optional()
    .matches(/^[a-zA-Z0-9\/.-]+$/)
    .withMessage("Invalid MIME type"),
  validateRequest
];
