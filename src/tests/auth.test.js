import request from "supertest";
import app from "../app.js";
import User from "../models/user.model.js";
import { sessionService } from "../config/redis.js";
import { generateToken } from "../utils/jwt.js";

// Test data
const testUser = {
  email: "<EMAIL>",
  password: "TestPass123",
  firstName: "Test",
  lastName: "User",
  username: "testuser123"
};

const testAdmin = {
  email: "<EMAIL>",
  password: "AdminPass123",
  firstName: "Admin",
  lastName: "User",
  username: "adminuser123",
  role: "admin"
};

describe("Authentication System Tests", () => {
  let userToken;
  let adminToken;
  let testUserId;

  beforeAll(async () => {
    // Clean up test data
    await User.deleteMany({ 
      email: { $in: [testUser.email, testAdmin.email] } 
    });
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({ 
      email: { $in: [testUser.email, testAdmin.email] } 
    });
  });

  describe("User Registration", () => {
    test("Should register a new user with email", async () => {
      const response = await request(app)
        .post("/api/auth/register")
        .send(testUser)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(testUser.email);
      expect(response.body.data.user.username).toBe(testUser.username);
      expect(response.body.data.token).toBeDefined();
      
      testUserId = response.body.data.user.id;
      userToken = response.body.data.token;
    });

    test("Should not register user with existing email", async () => {
      const response = await request(app)
        .post("/api/auth/register")
        .send(testUser)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("already exists");
    });

    test("Should not register user with invalid email", async () => {
      const response = await request(app)
        .post("/api/auth/register")
        .send({
          ...testUser,
          email: "invalid-email",
          username: "different123"
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test("Should not register user with weak password", async () => {
      const response = await request(app)
        .post("/api/auth/register")
        .send({
          ...testUser,
          password: "weak",
          email: "<EMAIL>",
          username: "weakuser123"
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe("User Login", () => {
    test("Should login with valid credentials", async () => {
      const response = await request(app)
        .post("/api/auth/login")
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(testUser.email);
      expect(response.body.data.token).toBeDefined();
    });

    test("Should not login with invalid email", async () => {
      const response = await request(app)
        .post("/api/auth/login")
        .send({
          email: "<EMAIL>",
          password: testUser.password
        })
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test("Should not login with invalid password", async () => {
      const response = await request(app)
        .post("/api/auth/login")
        .send({
          email: testUser.email,
          password: "wrongpassword"
        })
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe("Protected Routes", () => {
    test("Should access protected route with valid token", async () => {
      const response = await request(app)
        .get("/api/auth/me")
        .set("Authorization", `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(testUser.email);
    });

    test("Should not access protected route without token", async () => {
      const response = await request(app)
        .get("/api/auth/me")
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test("Should not access protected route with invalid token", async () => {
      const response = await request(app)
        .get("/api/auth/me")
        .set("Authorization", "Bearer invalid-token")
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe("Password Reset", () => {
    test("Should send password reset email", async () => {
      const response = await request(app)
        .post("/api/auth/forgot-password")
        .send({ email: testUser.email })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain("reset email sent");
    });

    test("Should not send reset email for non-existent user", async () => {
      const response = await request(app)
        .post("/api/auth/forgot-password")
        .send({ email: "<EMAIL>" })
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe("User Management", () => {
    beforeAll(async () => {
      // Create admin user for testing
      const adminUser = await User.create({
        ...testAdmin,
        password: await bcrypt.hash(testAdmin.password, 10),
        isEmailVerified: true
      });
      adminToken = generateToken(adminUser._id);
    });

    test("Should get user list as admin", async () => {
      const response = await request(app)
        .get("/api/users")
        .set("Authorization", `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.users)).toBe(true);
    });

    test("Should not get user list as regular user", async () => {
      const response = await request(app)
        .get("/api/users")
        .set("Authorization", `Bearer ${userToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
    });
  });

  describe("Subscription Management", () => {
    test("Should get subscription summary", async () => {
      const response = await request(app)
        .get("/api/auth/me")
        .set("Authorization", `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data.user.subscriptionSummary).toBeDefined();
      expect(response.body.data.user.isPro).toBe(false);
      expect(response.body.data.user.isLifetimePro).toBe(false);
    });
  });

  describe("Redis Session Management", () => {
    test("Should store and retrieve session data", async () => {
      const sessionId = "test-session-123";
      const sessionData = { userId: testUserId, email: testUser.email };

      // Store session
      const stored = await sessionService.setSession(sessionId, sessionData, 3600);
      expect(stored).toBe(true);

      // Retrieve session
      const retrieved = await sessionService.getSession(sessionId);
      expect(retrieved).toEqual(sessionData);

      // Delete session
      const deleted = await sessionService.deleteSession(sessionId);
      expect(deleted).toBe(true);

      // Verify deletion
      const afterDelete = await sessionService.getSession(sessionId);
      expect(afterDelete).toBe(null);
    });

    test("Should handle rate limiting", async () => {
      const identifier = "test-rate-limit";
      
      // First request should be allowed
      const first = await sessionService.checkRateLimit(identifier, 2, 60);
      expect(first.allowed).toBe(true);
      expect(first.remaining).toBe(1);

      // Second request should be allowed
      const second = await sessionService.checkRateLimit(identifier, 2, 60);
      expect(second.allowed).toBe(true);
      expect(second.remaining).toBe(0);

      // Third request should be blocked
      const third = await sessionService.checkRateLimit(identifier, 2, 60);
      expect(third.allowed).toBe(false);
      expect(third.remaining).toBe(0);
    });
  });
});
