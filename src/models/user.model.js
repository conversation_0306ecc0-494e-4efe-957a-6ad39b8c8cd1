import mongoose from "mongoose";
import { ROLES } from "../utils/constants.js";

const userSchema = new mongoose.Schema(
  {
    // Authentication Details
    email: { type: String, required: true, unique: true },
    username: { type: String, unique: true, required: true },
    password: { type: String }, // For email auth
    
    // OAuth providers support
    oauthProviders: [
      {
        provider: { type: String, enum: ["google", "github"], required: true },
        provider_id: { type: String, required: true },
        linked_at: { type: Date, default: Date.now },
      },
    ],



    // Profile Details
    firstName: { type: String },
    lastName: { type: String },
    profileImage: { type: String },
    phoneNumber: { type: String },

    // Role and Access
    role: { type: String, enum: Object.values(ROLES), default: ROLES.USER },
    isDeleted: { type: Boolean, default: false },

    // Account Suspension
    isSuspended: { type: Boolean, default: false },
    suspensionReason: { type: String },
    suspendedAt: { type: Date },
    suspendedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    suspensionExpiresAt: { type: Date },
    suspensionHistory: [{
      reason: String,
      suspendedAt: { type: Date, default: Date.now },
      suspendedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
      expiresAt: Date,
      liftedAt: Date,
      liftedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
      notes: String
    }],

    // Security and Account Monitoring
    securityFlags: {
      accountSharingDetected: { type: Boolean, default: false },
      suspiciousActivityDetected: { type: Boolean, default: false },
      multipleFailedLogins: { type: Boolean, default: false },
      requiresTwoFactor: { type: Boolean, default: false },
      lastSecurityCheck: Date
    },

    // Login tracking
    loginAttempts: {
      count: { type: Number, default: 0 },
      lastAttempt: Date,
      lockedUntil: Date
    },

    // Status and Metadata
    lastLogin: { type: Date },
    isEmailVerified: { type: Boolean, default: false },
    emailVerificationToken: { type: String },
    passwordResetToken: { type: String },
    passwordResetExpires: { type: Date },

    // Simplified Subscription Fields
    isPro: { type: Boolean, default: false },
    isLifetimePro: { type: Boolean, default: false },
    subscriptionStartDate: { type: Date },
    subscriptionEndDate: { type: Date },

    // Current plan reference (we already have this, no need for currentPlanDetails)
    currentPlan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan"
    },
  },
  { timestamps: true }
);

// Method to check if user has access to a product
userSchema.methods.hasAccessToProduct = function(product) {
  // Free products are accessible to everyone
  if (!product.isPaid) return true;

  // Check if user has active subscription
  return this.hasActiveSubscription();
};

// Check if user has active subscription
userSchema.methods.hasActiveSubscription = function() {
  if (this.isLifetimePro) return true;
  return this.isPro && this.subscriptionEndDate && this.subscriptionEndDate > new Date();
};

// Check if user is lifetime pro
userSchema.methods.isLifetime = function() {
  return this.isLifetimePro;
};

// Check if subscription is expired
userSchema.methods.isSubscriptionExpired = function() {
  if (this.isLifetimePro) return false;
  if (!this.subscriptionEndDate) return true;
  return this.subscriptionEndDate < new Date();
};

// Activate subscription
userSchema.methods.activateSubscription = function(plan, startDate = new Date()) {
  this.isPro = true;
  this.subscriptionStartDate = startDate;
  this.currentPlan = plan._id;

  // Handle lifetime subscription
  if (plan.duration === "lifetime") {
    this.isLifetimePro = true;
    this.subscriptionEndDate = null;
  } else {
    // Calculate end date based on duration
    const endDate = new Date(startDate);
    if (plan.duration === "monthly") {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (plan.duration === "yearly") {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }
    this.subscriptionEndDate = endDate;
  }

  return this.save();
};

// Cancel subscription
userSchema.methods.cancelSubscription = function() {
  this.isPro = false;
  this.isLifetimePro = false;
  this.subscriptionEndDate = null;
  this.currentPlan = null;
  return this.save();
};

// Renew subscription
userSchema.methods.renewSubscription = function(plan) {
  if (this.isLifetimePro) {
    throw new Error("Lifetime subscriptions cannot be renewed");
  }

  const currentEndDate = this.subscriptionEndDate || new Date();
  const newEndDate = new Date(currentEndDate);

  if (plan.duration === "monthly") {
    newEndDate.setMonth(newEndDate.getMonth() + 1);
  } else if (plan.duration === "yearly") {
    newEndDate.setFullYear(newEndDate.getFullYear() + 1);
  }

  this.subscriptionEndDate = newEndDate;
  this.isPro = true;
  this.currentPlan = plan._id;

  return this.save();
};



// Get subscription summary
userSchema.methods.getSubscriptionSummary = function() {
  return {
    isPro: this.isPro,
    isLifetime: this.isLifetimePro,
    startDate: this.subscriptionStartDate,
    endDate: this.subscriptionEndDate,
    hasActiveSubscription: this.hasActiveSubscription(),
    isExpired: this.isSubscriptionExpired(),
    currentPlan: this.currentPlan
  };
};

// OAuth Provider Management Methods

// Check if user has a specific OAuth provider linked
userSchema.methods.hasOAuthProvider = function(provider) {
  return this.oauthProviders.some(oauth => oauth.provider === provider);
};

// Link a new OAuth provider
userSchema.methods.linkOAuthProvider = function(provider, providerId) {
  // Check if provider is already linked
  if (this.hasOAuthProvider(provider)) {
    throw new Error(`${provider} account is already linked`);
  }

  this.oauthProviders.push({
    provider,
    provider_id: providerId,
    linked_at: new Date()
  });

  return this.save();
};

// Unlink an OAuth provider
userSchema.methods.unlinkOAuthProvider = function(provider) {
  // Ensure user has password or at least one other OAuth provider
  const remainingProviders = this.oauthProviders.filter(oauth => oauth.provider !== provider);
  
  if (!this.password && remainingProviders.length === 0) {
    throw new Error('Cannot unlink last authentication method. Please set a password first.');
  }

  this.oauthProviders = remainingProviders;
  return this.save();
};

// Get OAuth provider info
userSchema.methods.getOAuthProvider = function(provider) {
  return this.oauthProviders.find(oauth => oauth.provider === provider);
};

// Check if user can authenticate with email/password
userSchema.methods.canAuthenticateWithPassword = function() {
  return !!this.password;
};

// Check primary authentication method
userSchema.methods.getPrimaryAuthMethod = function() {
  if (this.password) return 'email';
  if (this.oauthProviders.length > 0) return this.oauthProviders[0].provider;
  return 'unknown';
};

// Suspension Management Methods

// Check if user is currently suspended
userSchema.methods.isCurrentlySuspended = function() {
  if (!this.isSuspended) return false;

  // Check if suspension has expired
  if (this.suspensionExpiresAt && this.suspensionExpiresAt <= new Date()) {
    return false;
  }

  return true;
};

// Suspend user account
userSchema.methods.suspendAccount = function(reason, suspendedBy, expiresAt = null, notes = null) {
  this.isSuspended = true;
  this.suspensionReason = reason;
  this.suspendedAt = new Date();
  this.suspendedBy = suspendedBy;
  this.suspensionExpiresAt = expiresAt;

  // Add to suspension history
  this.suspensionHistory.push({
    reason,
    suspendedAt: new Date(),
    suspendedBy,
    expiresAt,
    notes
  });

  return this.save();
};

// Lift suspension
userSchema.methods.liftSuspension = function(liftedBy, notes = null) {
  this.isSuspended = false;
  this.suspensionReason = null;
  this.suspendedAt = null;
  this.suspendedBy = null;
  this.suspensionExpiresAt = null;

  // Update latest suspension history entry
  const latestSuspension = this.suspensionHistory[this.suspensionHistory.length - 1];
  if (latestSuspension && !latestSuspension.liftedAt) {
    latestSuspension.liftedAt = new Date();
    latestSuspension.liftedBy = liftedBy;
    if (notes) latestSuspension.notes = (latestSuspension.notes || '') + ` | Lifted: ${notes}`;
  }

  return this.save();
};

// Check and auto-lift expired suspensions
userSchema.methods.checkSuspensionExpiry = function() {
  if (this.isSuspended && this.suspensionExpiresAt && this.suspensionExpiresAt <= new Date()) {
    return this.liftSuspension(null, 'Auto-lifted due to expiration');
  }
  return Promise.resolve(this);
};

// Security flag management
userSchema.methods.flagAccountSharing = function() {
  this.securityFlags.accountSharingDetected = true;
  this.securityFlags.lastSecurityCheck = new Date();
  return this.save();
};

userSchema.methods.flagSuspiciousActivity = function() {
  this.securityFlags.suspiciousActivityDetected = true;
  this.securityFlags.lastSecurityCheck = new Date();
  return this.save();
};

// Login attempt tracking
userSchema.methods.incrementLoginAttempts = function() {
  // If we have a previous failed attempt and it's been more than 2 hours, reset
  if (this.loginAttempts.lastAttempt &&
      Date.now() - this.loginAttempts.lastAttempt > 2 * 60 * 60 * 1000) {
    this.loginAttempts.count = 1;
  } else {
    this.loginAttempts.count += 1;
  }

  this.loginAttempts.lastAttempt = new Date();

  // Lock account after 5 failed attempts for 30 minutes
  if (this.loginAttempts.count >= 5) {
    this.loginAttempts.lockedUntil = new Date(Date.now() + 30 * 60 * 1000);
    this.securityFlags.multipleFailedLogins = true;
  }

  return this.save();
};

userSchema.methods.resetLoginAttempts = function() {
  this.loginAttempts.count = 0;
  this.loginAttempts.lastAttempt = null;
  this.loginAttempts.lockedUntil = null;
  return this.save();
};

userSchema.methods.isAccountLocked = function() {
  return this.loginAttempts.lockedUntil && this.loginAttempts.lockedUntil > new Date();
};

// Static methods for admin operations
userSchema.statics.getSuspendedUsers = function() {
  return this.find({ isSuspended: true }).populate('suspendedBy', 'firstName lastName email');
};

userSchema.statics.getUsersWithSecurityFlags = function() {
  return this.find({
    $or: [
      { 'securityFlags.accountSharingDetected': true },
      { 'securityFlags.suspiciousActivityDetected': true },
      { 'securityFlags.multipleFailedLogins': true }
    ]
  });
};

// Pre-save middleware to check suspension expiry
userSchema.pre('save', function(next) {
  if (this.isSuspended && this.suspensionExpiresAt && this.suspensionExpiresAt <= new Date()) {
    this.isSuspended = false;
    this.suspensionReason = null;
    this.suspendedAt = null;
    this.suspendedBy = null;
    this.suspensionExpiresAt = null;
  }
  next();
});

export default mongoose.model("User", userSchema);
