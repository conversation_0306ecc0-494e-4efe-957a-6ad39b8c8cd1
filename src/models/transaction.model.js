import mongoose from "mongoose";
import { 
  TRANSACTION_TYPES, 
  TRANSACTION_STATUSES, 
  PAYMENT_METHODS, 
  DEFAULT_CURRENCY 
} from "../utils/constants.js";

const transactionSchema = new mongoose.Schema(
  {
    // Core transaction details
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(TRANSACTION_TYPES),
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(TRANSACTION_STATUSES),
      default: TRANSACTION_STATUSES.PENDING,
    },
    
    // Financial details
    amount: { 
      type: Number, 
      required: true,
      min: 0,
    },
    currency: { 
      type: String, 
      required: true, 
      default: DEFAULT_CURRENCY 
    },
    originalAmount: {
      type: Number,
      required: true,
      min: 0,
    },
    discountAmount: {
      type: Number,
      default: 0,
      min: 0,
    },
    
    // Payment details
    paymentMethod: {
      type: String,
      enum: Object.values(PAYMENT_METHODS),
      required: true,
    },
    paymentGateway: {
      type: String,
      enum: ["stripe", "razorpay", "paypal", "manual"],
    },
    gatewayTransactionId: {
      type: String,
    },
    gatewaySubscriptionId: {
      type: String, // For recurring subscriptions
    },
    gatewayResponse: {
      type: Object,
    },
    
    // Plan and coupon references
    plan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan",
      required: true,
    },
    coupon: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Coupon",
    },
    
    // Transaction metadata
    description: {
      type: String,
    },
    internalNotes: {
      type: String,
    },
    
    // Dates
    processedAt: Date,
    failedAt: Date,
    expiresAt: Date,
    
    // Simplified metadata for additional data
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes

transactionSchema.index({ gatewayTransactionId: 1 }, { sparse: true });
transactionSchema.index({ gatewaySubscriptionId: 1 }, { sparse: true });

// Virtuals
transactionSchema.virtual('isFree').get(function() {
  return this.amount === 0 && this.originalAmount > 0;
});

transactionSchema.virtual('discountPercentage').get(function() {
  if (this.originalAmount === 0) return 0;
  return Math.round((this.discountAmount / this.originalAmount) * 100);
});

// Pre-save middleware
transactionSchema.pre('save', function(next) {
  if (!this.description && this.plan) {
    const action = this.type.replace('_', ' ').toLowerCase();
    this.description = `${action} - Plan ${this.plan}`;
  }

  if (this.status === TRANSACTION_STATUSES.COMPLETED && !this.processedAt) {
    this.processedAt = new Date();
  }

  if (this.status === TRANSACTION_STATUSES.FAILED && !this.failedAt) {
    this.failedAt = new Date();
  }

  next();
});

// Static method
transactionSchema.statics.createSubscriptionTransaction = function(data) {
  const {
    user,
    plan,
    coupon = null,
    paymentMethod,
    paymentGateway,
    gatewaySubscriptionId,
    type = TRANSACTION_TYPES.SUBSCRIPTION_PURCHASE,
    metadata = {}
  } = data;

  let amount = plan.price;
  let discountAmount = 0;
  let originalAmount = plan.price;

  if (coupon) {
    if (coupon.discountType === 'percentage') {
      discountAmount = (originalAmount * coupon.discountValue) / 100;
    } else if (coupon.discountType === 'fixed') {
      discountAmount = Math.min(coupon.discountValue, originalAmount);
    }
    amount = Math.max(0, originalAmount - discountAmount);
  }

  const transactionMetadata = {
    ...metadata,
    planSnapshot: {
      name: plan.name,
      price: plan.price,
      duration: plan.duration,
      benefits: plan.benefits,
    },
    couponSnapshot: coupon ? {
      code: coupon.code,
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
    } : null
  };

  return new this({
    user,
    plan: plan._id,
    coupon: coupon?._id,
    type,
    amount,
    originalAmount,
    discountAmount,
    currency: plan.currency || DEFAULT_CURRENCY,
    paymentMethod: amount === 0 ? PAYMENT_METHODS.FREE : paymentMethod,
    paymentGateway,
    gatewaySubscriptionId,
    metadata: transactionMetadata,
    description: `${type.replace('_', ' ')} - ${plan.name} plan`,
  });
};

export default mongoose.model("Transaction", transactionSchema);
