import mongoose from "mongoose";

const fileSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, trim: true },
    originalName: { type: String, required: true }, // Original filename when uploaded
    
    // Folder relationship
    folder: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Folder",
      required: true
    },
    
    // Storage information
    url: { type: String, required: true }, // File URL
    key: { type: String }, // Storage key (S3, etc.)
    fileSize: { type: Number, required: true }, // Size in bytes
    mimeType: { type: String, required: true }, // MIME type
    
    // File type categorization
    fileType: {
      type: String,
      enum: ["image", "video", "audio", "document", "archive", "code", "other"],
      required: true
    },
    
    // Image-specific metadata
    dimensions: {
      width: { type: Number },
      height: { type: Number }
    },
    
    // Source information
    source: {
      type: String,
      enum: ["S3", "UploadThing", "Unsplash", "External", "Local"],
      default: "S3"
    },
    sourceDetails: { type: String }, // Additional source metadata
    
    // File integrity
    checksum: { type: String }, // For file verification
    
    // Content Management
    title: { type: String }, // Display title (can be different from name)
    description: { type: String }, // File description
    tags: [{ type: String, trim: true }], // Tags for categorization
    category: { type: String }, // Category classification
    
    // Access Control
    isPublic: { type: Boolean, default: true },
    isDeleted: { type: Boolean, default: false }, // Soft delete
    isFeatured: { type: Boolean, default: false }, // Featured content
    
    // User Management
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    }, // Who uploaded the file
    
    // Statistics
    views: { type: Number, default: 0 },
    downloads: { type: Number, default: 0 },
    
    // Additional metadata
    metadata: {
      takenAt: { type: Date }, // For photos with EXIF data
      exifData: { type: mongoose.Schema.Types.Mixed }, // Raw EXIF data
      customData: { type: mongoose.Schema.Types.Mixed }, // Any additional data
    }
  },
  {
    timestamps: true,
  }
);

// Indexes for performance
fileSchema.index({ folder: 1, name: 1 }); // Files in folder
fileSchema.index({ createdBy: 1 }); // User's files
fileSchema.index({ uploadedBy: 1 }); // Uploaded by user
fileSchema.index({ mimeType: 1 }); // File type filtering
fileSchema.index({ fileType: 1 }); // File category filtering
fileSchema.index({ tags: 1 }); // Tag-based searches
fileSchema.index({ isPublic: 1, isFeatured: 1 }); // Public/featured content
fileSchema.index({ isDeleted: 1 }); // Soft delete filtering

// Virtual for getting file extension
fileSchema.virtual("extension").get(function() {
  return this.originalName.split('.').pop().toLowerCase();
});

// Virtual for getting human readable file size
fileSchema.virtual("humanFileSize").get(function() {
  const bytes = this.fileSize;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Pre-save middleware to determine file type
fileSchema.pre('save', function(next) {
  if (this.isModified('mimeType')) {
    const mimeType = this.mimeType.toLowerCase();
    
    if (mimeType.startsWith('image/')) {
      this.fileType = 'image';
    } else if (mimeType.startsWith('video/')) {
      this.fileType = 'video';
    } else if (mimeType.startsWith('audio/')) {
      this.fileType = 'audio';
    } else if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) {
      this.fileType = 'document';
    } else if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) {
      this.fileType = 'archive';
    } else if (mimeType.includes('javascript') || mimeType.includes('json') || mimeType.includes('xml')) {
      this.fileType = 'code';
    } else {
      this.fileType = 'other';
    }
  }
  next();
});

// Method to increment view count
fileSchema.methods.incrementViews = function() {
  this.views += 1;
  return this.save();
};

// Method to increment download count
fileSchema.methods.incrementDownloads = function() {
  this.downloads += 1;
  return this.save();
};

// Method to check if file is an image
fileSchema.methods.isImage = function() {
  return this.fileType === 'image';
};

// Method to check if file is a video
fileSchema.methods.isVideo = function() {
  return this.fileType === 'video';
};

// Static method to find files by type
fileSchema.statics.findByType = function(fileType, options = {}) {
  return this.find({ 
    fileType, 
    isDeleted: false, 
    ...options 
  });
};

// Static method to find files by mime type pattern
fileSchema.statics.findByMimeType = function(mimeTypePattern, options = {}) {
  return this.find({ 
    mimeType: { $regex: mimeTypePattern, $options: "i" },
    isDeleted: false, 
    ...options 
  });
};

export default mongoose.model("File", fileSchema);
