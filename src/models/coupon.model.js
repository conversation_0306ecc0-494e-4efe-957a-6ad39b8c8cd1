import mongoose from "mongoose";

const couponSchema = new mongoose.Schema(
  {
    code: { type: String, required: true, unique: true },
    discountType: {
      type: String,
      enum: ["percentage", "fixed"],
      required: true,
    },
    discountValue: { type: Number, required: true },
    maxRedemptions: { type: Number },
    currentRedemptions: { type: Number, default: 0 },
    validFrom: { type: Date, required: true },
    validUntil: { type: Date, required: true },
    isActive: { type: Boolean, default: true },
    expirationDate: { type: Date }, // Add expiration date for consistency
  },
  { timestamps: true }
);

export default mongoose.model("Coupon", couponSchema);
