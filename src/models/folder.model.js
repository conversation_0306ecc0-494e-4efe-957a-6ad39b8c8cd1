import mongoose from "mongoose";

const folderSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, trim: true },
    slug: { type: String, required: true, trim: true },

    // Hierarchy
    parent: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Folder",
      default: null
    },
    path: { type: String, required: true }, // Full path for quick access
    level: { type: Number, default: 0 }, // Depth level for easier queries

    // Content Management
    title: { type: String }, // Display title (can be different from name)
    description: { type: String }, // Folder description
    tags: [{ type: String, trim: true }], // Tags for categorization
    category: { type: String }, // Category classification

    // Access Control
    isPublic: { type: Boolean, default: true },
    isDeleted: { type: Boolean, default: false }, // Soft delete
    isFeatured: { type: Boolean, default: false }, // Featured content

    // User Management
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },

    // Statistics
    totalFiles: { type: Number, default: 0 }, // Count of files in this folder
    totalSubfolders: { type: Number, default: 0 }, // Count of subfolders
    totalSize: { type: Number, default: 0 }, // Total size of all files in bytes

    // Additional metadata
    metadata: {
      customData: { type: mongoose.Schema.Types.Mixed }, // Any additional data
    }
  },
  {
    timestamps: true,
  }
);

// Indexes for performance
folderSchema.index({ parent: 1, name: 1 }); // Folder contents
folderSchema.index({ path: 1 }, { unique: true }); // Path uniqueness
folderSchema.index({ createdBy: 1 }); // User's folders
folderSchema.index({ tags: 1 }); // Tag-based searches
folderSchema.index({ isPublic: 1, isFeatured: 1 }); // Public/featured content
folderSchema.index({ isDeleted: 1 }); // Soft delete filtering

// Virtual for getting full path
folderSchema.virtual("fullPath").get(function() {
  return this.path;
});

// Pre-save middleware to update path
folderSchema.pre('save', async function(next) {
  if (this.isModified('parent') || this.isModified('name')) {
    if (this.parent) {
      const parentFolder = await this.constructor.findById(this.parent);
      if (parentFolder) {
        this.path = `${parentFolder.path}/${this.name}`;
        this.level = parentFolder.level + 1;
      }
    } else {
      this.path = `/${this.name}`;
      this.level = 0;
    }
  }
  next();
});

// Method to get all subfolders
folderSchema.methods.getSubfolders = function() {
  return this.constructor.find({ parent: this._id, isDeleted: false });
};

// Method to get all files in this folder
folderSchema.methods.getFiles = function() {
  const File = mongoose.model('File');
  return File.find({ folder: this._id, isDeleted: false });
};

// Static method to get folder tree
folderSchema.statics.getFolderTree = function(userId, parentId = null) {
  return this.find({
    createdBy: userId,
    parent: parentId,
    isDeleted: false
  }).sort({ name: 1 });
};

export default mongoose.model("Folder", folderSchema);
