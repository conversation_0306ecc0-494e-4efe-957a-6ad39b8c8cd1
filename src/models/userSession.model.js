import mongoose from "mongoose";

// Session status enum
export const SESSION_STATUS = {
  ACTIVE: "active",
  EXPIRED: "expired",
  TERMINATED: "terminated",
  SUSPICIOUS: "suspicious"
};

// Device types enum
export const DEVICE_TYPES = {
  DESKTOP: "desktop",
  MOBILE: "mobile",
  TABLET: "tablet",
  UNKNOWN: "unknown"
};

const userSessionSchema = new mongoose.Schema(
  {
    // User reference
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true
    },

    // Session identification
    sessionId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    
    jwtTokenId: {
      type: String, // JWT jti claim for token identification
      required: true,
      index: true
    },

    // Session status and metadata
    status: {
      type: String,
      enum: Object.values(SESSION_STATUS),
      default: SESSION_STATUS.ACTIVE,
      index: true
    },

    // Device and browser information
    deviceInfo: {
      userAgent: String,
      deviceType: {
        type: String,
        enum: Object.values(DEVICE_TYPES),
        default: DEVICE_TYPES.UNKNOWN
      },
      browser: {
        name: String,
        version: String
      },
      os: {
        name: String,
        version: String
      },
      deviceFingerprint: String, // Unique device identifier
    },

    // Network and location information
    networkInfo: {
      ipAddress: {
        type: String,
        required: true,
        index: true
      },
      country: String,
      region: String,
      city: String,
      timezone: String,
      isp: String,
      isVPN: { type: Boolean, default: false },
      isProxy: { type: Boolean, default: false },
    },

    // Session timing
    loginTime: {
      type: Date,
      default: Date.now,
      index: true
    },
    
    lastActivity: {
      type: Date,
      default: Date.now,
      index: true
    },
    
    expiresAt: {
      type: Date,
      required: true
    },
    
    logoutTime: Date,

    // Security flags
    securityFlags: {
      isFirstTimeDevice: { type: Boolean, default: false },
      isNewLocation: { type: Boolean, default: false },
      isSuspiciousActivity: { type: Boolean, default: false },
      requiresVerification: { type: Boolean, default: false },
      multipleSimultaneousLogins: { type: Boolean, default: false },
    },

    // Activity tracking
    activityCount: {
      type: Number,
      default: 0
    },
    
    lastEndpoint: String,
    
    // Termination reason
    terminationReason: {
      type: String,
      enum: [
        "user_logout",
        "token_expired", 
        "admin_terminated",
        "security_violation",
        "account_suspended",
        "multiple_sessions_detected",
        "suspicious_activity"
      ]
    },

    // Additional metadata
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better query performance
userSessionSchema.index({ user: 1, status: 1 });
userSessionSchema.index({ user: 1, loginTime: -1 });
userSessionSchema.index({ "networkInfo.ipAddress": 1, loginTime: -1 });
userSessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index
userSessionSchema.index({ status: 1, lastActivity: -1 });

// Virtual for session duration
userSessionSchema.virtual('duration').get(function() {
  const endTime = this.logoutTime || new Date();
  return endTime - this.loginTime;
});

// Virtual for checking if session is active
userSessionSchema.virtual('isActive').get(function() {
  return this.status === SESSION_STATUS.ACTIVE && 
         this.expiresAt > new Date() && 
         !this.logoutTime;
});

// Method to check if session is expired
userSessionSchema.methods.isExpired = function() {
  return this.expiresAt <= new Date() || this.status === SESSION_STATUS.EXPIRED;
};

// Method to terminate session
userSessionSchema.methods.terminate = function(reason = "user_logout") {
  this.status = SESSION_STATUS.TERMINATED;
  this.logoutTime = new Date();
  this.terminationReason = reason;
  return this.save();
};

// Method to mark as suspicious
userSessionSchema.methods.markSuspicious = function(reason) {
  this.status = SESSION_STATUS.SUSPICIOUS;
  this.securityFlags.isSuspiciousActivity = true;
  this.metadata.suspiciousReason = reason;
  this.metadata.suspiciousAt = new Date();
  return this.save();
};

// Method to update activity
userSessionSchema.methods.updateActivity = function(endpoint) {
  this.lastActivity = new Date();
  this.activityCount += 1;
  if (endpoint) this.lastEndpoint = endpoint;
  return this.save();
};

// Static method to get active sessions for user
userSessionSchema.statics.getActiveSessions = function(userId) {
  return this.find({
    user: userId,
    status: SESSION_STATUS.ACTIVE,
    expiresAt: { $gt: new Date() }
  }).sort({ lastActivity: -1 });
};

// Static method to terminate all sessions for user
userSessionSchema.statics.terminateAllUserSessions = function(userId, reason = "admin_terminated") {
  return this.updateMany(
    { 
      user: userId, 
      status: SESSION_STATUS.ACTIVE 
    },
    { 
      status: SESSION_STATUS.TERMINATED,
      logoutTime: new Date(),
      terminationReason: reason
    }
  );
};

// Static method to clean expired sessions
userSessionSchema.statics.cleanExpiredSessions = function() {
  return this.updateMany(
    {
      status: SESSION_STATUS.ACTIVE,
      expiresAt: { $lte: new Date() }
    },
    {
      status: SESSION_STATUS.EXPIRED,
      terminationReason: "token_expired"
    }
  );
};

// Static method to detect multiple simultaneous sessions
userSessionSchema.statics.detectMultipleSessions = function(userId, threshold = 3) {
  return this.countDocuments({
    user: userId,
    status: SESSION_STATUS.ACTIVE,
    expiresAt: { $gt: new Date() }
  }).then(count => count >= threshold);
};

// Static method to get sessions by IP
userSessionSchema.statics.getSessionsByIP = function(ipAddress, timeWindow = 24) {
  const since = new Date(Date.now() - (timeWindow * 60 * 60 * 1000));
  return this.find({
    "networkInfo.ipAddress": ipAddress,
    loginTime: { $gte: since }
  }).populate("user", "email username");
};

export default mongoose.model("UserSession", userSessionSchema);
