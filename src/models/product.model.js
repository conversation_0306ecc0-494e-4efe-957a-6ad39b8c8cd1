import mongoose from "mongoose";

const productSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    fullName: { type: String, required: true, unique: true },
    slug: { type: String, required: true, unique: true },
    description: { type: String },
    creator: { type: String },

    // Category and Subcategory
    category: { type: String, required: true },
    subCategory: [{ type: String }],

    // Tech Stack
    techStack: [{ type: String, required: true }],

    // Tags for additional classification or search
    // tags: [{ type: String }],

    // Images as a comma-separated string
    images: { type: String },

    // Pricing and Access Control
    price: { type: Number, default: 0 },
    isPaid: { type: Boolean, default: false },
    isFeatured: { type: Boolean, default: false },
    discount: { type: Number, default: 0 },
    is_pro: { type: Boolean, default: false },
    isExclusive: { type: Boolean, default: false },

    // Resource Links
    previewUrl: { type: String },
    githubUrl: { type: String },
    paymentLink: { type: String },

    // Secure content storage (for paid products)
    secureContent: {
      downloadUrl: { type: String },
      fileSize: { type: Number },
      fileName: { type: String },
      fileType: { type: String },
      checksum: { type: String },
    },

    // Engagement Metrics
    downloads: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    rating: { type: Number, default: 0, min: 0, max: 5 },

    // Features and Highlights
    keyFeatures: { type: mongoose.Schema.Types.Mixed },
    highlights: { type: mongoose.Schema.Types.Mixed },

    // Product Status
    status: {
      type: String,
      enum: ["active", "inactive", "archived"],
      default: "active",
    },
  },
  { timestamps: true }
);

// Indexing fields for improved query performance
productSchema.index({ category: 1 });
productSchema.index({ status: 1 });
productSchema.index({ is_pro: 1 });

export default mongoose.model("Product", productSchema);
