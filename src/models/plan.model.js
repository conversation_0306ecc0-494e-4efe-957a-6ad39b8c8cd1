import mongoose from "mongoose";

// Plan types for business model
export const PLAN_TYPES = {
  FREE: "free",
  INDIVIDUAL: "individual",
  STARTUP: "startup",
  ENTERPRISE: "enterprise" // UI-only, not stored in DB
};

// Plan tiers for subscription levels
export const PLAN_TIERS = {
  FREE: "free",
  BASIC: "basic",
  PRO: "pro",
  ENTERPRISE: "enterprise"
};

const planSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, unique: true }, // Name of the plan (e.g., "Free", "Individual Monthly", "Startup Yearly")
    description: { type: String }, // Description of the plan
    price: { type: Number, required: true }, // Price for the plan (0 for free plans)
    currency: { type: String, default: "USD" }, // Currency for the price
    duration: {
      type: String,
      enum: ["monthly", "yearly", "lifetime"],
      default: "monthly",
    }, // Plan duration (monthly, yearly, lifetime)

    // Payment Gateway Integration
    priceId: {
      type: String,
      sparse: true, // Allows multiple null values but enforces uniqueness for non-null values
      index: true
    }, // Payment gateway subscription plan ID (Stripe price_id, Razorpay plan_id, etc.)

    // Plan Classification
    planType: {
      type: String,
      enum: Object.values(PLAN_TYPES),
      required: true,
      default: PLAN_TYPES.FREE
    }, // Business model plan type (free, individual, startup, enterprise)

    tier: {
      type: String,
      enum: Object.values(PLAN_TIERS),
      required: true,
      default: PLAN_TIERS.FREE
    }, // Subscription tier level

    // Plan Status and Visibility
    isActive: { type: Boolean, default: true }, // Whether the plan is active or not
    isVisible: { type: Boolean, default: true }, // Whether the plan should be shown in UI
    isFeatured: { type: Boolean, default: false }, // Whether to highlight this plan

    // Plan Features and Benefits
    benefits: { type: mongoose.Schema.Types.Mixed }, // JSON data for plan benefits
    features: [{
      name: String,
      description: String,
      included: { type: Boolean, default: true },
      limit: Number // For features with usage limits
    }], // Structured features list

    // Usage Limits
    limits: {
      downloads: { type: Number, default: -1 }, // -1 for unlimited
      projects: { type: Number, default: -1 },
      storage: { type: Number, default: -1 }, // in MB
      apiCalls: { type: Number, default: -1 },
      teamMembers: { type: Number, default: 1 }
    },

    // Metadata
    sortOrder: { type: Number, default: 0 }, // For ordering plans in UI
    metadata: { type: mongoose.Schema.Types.Mixed }, // Additional plan metadata
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" }, // Reference to the admin who created the plan
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better query performance
planSchema.index({ planType: 1, duration: 1 });
planSchema.index({ tier: 1, isActive: 1 });
planSchema.index({ isActive: 1, isVisible: 1, sortOrder: 1 });

// Virtual for display name
planSchema.virtual('displayName').get(function() {
  if (this.planType === PLAN_TYPES.FREE) {
    return 'Free';
  }
  return `${this.name} (${this.duration})`;
});

// Method to check if plan is free
planSchema.methods.isFree = function() {
  return this.planType === PLAN_TYPES.FREE || this.price === 0;
};

// Method to check if plan requires payment gateway
planSchema.methods.requiresPaymentGateway = function() {
  return !this.isFree() && this.priceId;
};

// Static method to get plans by type
planSchema.statics.getByType = function(planType, options = {}) {
  const query = {
    planType,
    isActive: true,
    ...options
  };
  return this.find(query).sort({ sortOrder: 1, price: 1 });
};

// Static method to get visible plans for pricing page
planSchema.statics.getVisiblePlans = function() {
  return this.find({
    isActive: true,
    isVisible: true
  }).sort({ sortOrder: 1, price: 1 });
};

export default mongoose.model("Plan", planSchema);
