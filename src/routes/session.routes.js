import express from "express";
import {
  getUserSessions,
  terminateSession,
  terminateOtherSessions,
  getAccountSharingStatus,
  reportSuspiciousActivity,
  getAllSessions,
  adminTerminateSession,
  suspendUserAccount,
  liftUserSuspension,
  getSecurityDashboard,
  manualSessionCleanup,
  getCleanupStats,
} from "../controllers/session.controller.js";
import { protect, authorize } from "../middlewares/auth.middleware.js";
import { 
  checkAccountSuspension,
  trackUserSession,
  sensitiveOperationRateLimit 
} from "../middlewares/accountSecurity.middleware.js";
import { body, param, query, validationResult } from "express-validator";

const router = express.Router();

// Validation middleware
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array(),
    });
  }
  next();
};

// Apply authentication to all routes
router.use(protect);
router.use(checkAccountSuspension);
router.use(trackUserSession);

// User session management routes

/**
 * @route GET /api/sessions
 * @desc Get current user's active sessions
 * @access Private
 */
router.get("/", getUserSessions);

/**
 * @route DELETE /api/sessions/:sessionId
 * @desc Terminate a specific session
 * @access Private
 */
router.delete(
  "/:sessionId",
  [
    param("sessionId")
      .notEmpty()
      .withMessage("Session ID is required")
      .isLength({ min: 32, max: 64 })
      .withMessage("Invalid session ID format"),
    body("reason")
      .optional()
      .isString()
      .withMessage("Reason must be a string")
      .isLength({ max: 255 })
      .withMessage("Reason must be less than 255 characters"),
  ],
  validateRequest,
  sensitiveOperationRateLimit,
  terminateSession
);

/**
 * @route DELETE /api/sessions/others
 * @desc Terminate all other sessions except current
 * @access Private
 */
router.delete("/others", sensitiveOperationRateLimit, terminateOtherSessions);

/**
 * @route GET /api/sessions/security/sharing-status
 * @desc Get account sharing detection status
 * @access Private
 */
router.get("/security/sharing-status", getAccountSharingStatus);

/**
 * @route POST /api/sessions/security/report
 * @desc Report suspicious activity
 * @access Private
 */
router.post(
  "/security/report",
  [
    body("description")
      .notEmpty()
      .withMessage("Description is required")
      .isString()
      .withMessage("Description must be a string")
      .isLength({ min: 10, max: 1000 })
      .withMessage("Description must be between 10 and 1000 characters"),
    body("sessionId")
      .optional()
      .isString()
      .withMessage("Session ID must be a string"),
  ],
  validateRequest,
  reportSuspiciousActivity
);

// Admin routes
router.use(authorize(["admin", "super_admin"]));

/**
 * @route GET /api/sessions/admin/all
 * @desc Get all sessions with filtering (admin only)
 * @access Admin
 */
router.get(
  "/admin/all",
  [
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("limit")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Limit must be between 1 and 100"),
    query("status")
      .optional()
      .isIn(["active", "expired", "terminated", "suspicious"])
      .withMessage("Invalid status value"),
    query("userId")
      .optional()
      .isMongoId()
      .withMessage("Invalid user ID format"),
    query("ipAddress")
      .optional()
      .isIP()
      .withMessage("Invalid IP address format"),
    query("suspicious")
      .optional()
      .isBoolean()
      .withMessage("Suspicious must be a boolean"),
  ],
  validateRequest,
  getAllSessions
);

/**
 * @route DELETE /api/sessions/admin/:sessionId
 * @desc Terminate session (admin only)
 * @access Admin
 */
router.delete(
  "/admin/:sessionId",
  [
    param("sessionId")
      .notEmpty()
      .withMessage("Session ID is required")
      .isLength({ min: 32, max: 64 })
      .withMessage("Invalid session ID format"),
    body("reason")
      .notEmpty()
      .withMessage("Reason is required for admin termination")
      .isString()
      .withMessage("Reason must be a string")
      .isLength({ max: 255 })
      .withMessage("Reason must be less than 255 characters"),
  ],
  validateRequest,
  adminTerminateSession
);

/**
 * @route POST /api/sessions/admin/suspend/:userId
 * @desc Suspend user account (admin only)
 * @access Admin
 */
router.post(
  "/admin/suspend/:userId",
  [
    param("userId")
      .notEmpty()
      .withMessage("User ID is required")
      .isMongoId()
      .withMessage("Invalid user ID format"),
    body("reason")
      .notEmpty()
      .withMessage("Suspension reason is required")
      .isString()
      .withMessage("Reason must be a string")
      .isLength({ min: 10, max: 500 })
      .withMessage("Reason must be between 10 and 500 characters"),
    body("expiresAt")
      .optional()
      .isISO8601()
      .withMessage("Expiration date must be a valid ISO 8601 date")
      .custom((value) => {
        if (value && new Date(value) <= new Date()) {
          throw new Error("Expiration date must be in the future");
        }
        return true;
      }),
    body("notes")
      .optional()
      .isString()
      .withMessage("Notes must be a string")
      .isLength({ max: 1000 })
      .withMessage("Notes must be less than 1000 characters"),
  ],
  validateRequest,
  suspendUserAccount
);

/**
 * @route POST /api/sessions/admin/lift-suspension/:userId
 * @desc Lift user suspension (admin only)
 * @access Admin
 */
router.post(
  "/admin/lift-suspension/:userId",
  [
    param("userId")
      .notEmpty()
      .withMessage("User ID is required")
      .isMongoId()
      .withMessage("Invalid user ID format"),
    body("notes")
      .optional()
      .isString()
      .withMessage("Notes must be a string")
      .isLength({ max: 1000 })
      .withMessage("Notes must be less than 1000 characters"),
  ],
  validateRequest,
  liftUserSuspension
);

/**
 * @route GET /api/sessions/admin/dashboard
 * @desc Get security dashboard data (admin only)
 * @access Admin
 */
router.get("/admin/dashboard", getSecurityDashboard);

/**
 * @route POST /api/sessions/admin/cleanup
 * @desc Manual session cleanup (admin only)
 * @access Admin
 */
router.post("/admin/cleanup", manualSessionCleanup);

/**
 * @route GET /api/sessions/admin/cleanup-stats
 * @desc Get session cleanup statistics (admin only)
 * @access Admin
 */
router.get("/admin/cleanup-stats", getCleanupStats);

export default router;
