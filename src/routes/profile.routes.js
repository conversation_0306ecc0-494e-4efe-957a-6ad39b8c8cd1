import express from "express";
import {
  updateProfile,
  changePassword,
} from "../controllers/user.controller.js";
import { protect } from "../middlewares/auth.middleware.js";
import { requireEmailVerification } from "../middlewares/emailVerification.middleware.js";
import {
  updateProfileValidator,
  changePasswordValidator,
} from "../validators/user.validator.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

const router = express.Router();

// All profile routes require authentication and email verification
router.use(protect);
router.use(requireEmailVerification);

// Profile management routes
router.put("/", updateProfileValidator, validateRequest, updateProfile); // Update current user profile
router.put("/change-password", changePasswordValidator, validateRequest, changePassword); // Change password

export default router;
