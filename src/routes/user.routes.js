import express from "express";
import {
  createUser,
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  assignSubscription,
  getUsersWithPagination,
  bulkDeleteUsers,
  bulkUpdateUserRoles,
  bulkUpdateSubscriptions,
  bulkVerifyEmails,
  toggleEmailVerification,
  toggleUserSuspension,
  forcePasswordReset,
  getUserActivity,
  getUserAnalytics,
  getRecentUserActivities,
} from "../controllers/user.controller.js";
import { authorize } from "../middlewares/auth.middleware.js";
import {
  createUserValidator,
  updateUserValidator,
  userIdValidator,
  assignSubscriptionValidator,
} from "../validators/user.validator.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

const router = express.Router();

// Admin-only routes
router.use(authorize(["admin", "super_admin"])); // Middleware to restrict access to admin only
router.post("/", createUserValidator, validateRequest, createUser); // Create user (admin only)
router.get("/", getUsers); // Get all users
router.get("/pagination", getUsersWithPagination); //fetch users with pagination
router.get("/analytics", getUserAnalytics); // Get user analytics
router.get("/recent-activities", getRecentUserActivities); // Get recent user activities
router.get("/:id", getUserById); // Get a single user
router.get("/:id/activity", getUserActivity); // Get user activity summary
router.put("/:id", updateUserValidator, validateRequest, updateUser); // Update user (self or admin)
router.delete("/:id", userIdValidator, validateRequest, deleteUser); // Delete user (admin only)

// User status management
router.patch("/:id/toggle-verification", toggleEmailVerification); // Toggle email verification
router.patch("/:id/toggle-suspension", toggleUserSuspension); // Suspend/unsuspend user
router.post("/:id/force-password-reset", forcePasswordReset); // Force password reset

// Bulk operations
router.post("/bulk/delete", bulkDeleteUsers); // Bulk delete users
router.post("/bulk/update-roles", bulkUpdateUserRoles); // Bulk update user roles
router.post("/bulk/update-subscriptions", bulkUpdateSubscriptions); // Bulk update subscriptions
router.post("/bulk/verify-emails", bulkVerifyEmails); // Bulk verify emails

// Subscription Management
router.post(
  "/:id/subscribe",
  assignSubscriptionValidator,
  validateRequest,
  assignSubscription
); // Assign subscription to user (admin only)

export default router;
