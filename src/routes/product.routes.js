import express from "express";
import {
  aggregateProducts,
  createProduct,
  deleteProduct,
  getAllProducts,
  getProductById,
  getProductBySlug,
  getProductsByCategory,
  getProductsCursor,
  getProductsPagination,
  getSubCategoriesCountByCategory,
  getSubCategoriesCount,
  updateProduct,
} from "../controllers/product.controller.js";
import { protect, authorize } from "../middlewares/auth.middleware.js";

import { validateProduct } from "../validators/product.validator.js";

const router = express.Router();

// Public routes (using global rate limiting)
router.get("/", getAllProducts);
router.get("/category/:category", getProductsByCategory);
router.get("/slug/:slug", getProductBySlug);
router.get("/cursor", getProductsCursor);
router.get("/aggregation", aggregateProducts);
router.get("/pagination", getProductsPagination);
router.get("/subcategories/count/:category", getSubCategoriesCountByCategory);
router.get("/subcategories/count", getSubCategoriesCount);
router.get("/:id", getProductById);

// Admin-only routes
router.use(protect);
router.use(authorize(["admin", "super_admin"]));
router.post("/", validateProduct, createProduct);
router.put("/:id", validateProduct, updateProduct);
router.delete("/:id", deleteProduct);

export default router;
