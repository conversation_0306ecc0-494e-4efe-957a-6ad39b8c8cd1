import express from "express";
import {
  createTransaction,
  getTransactions,
  getTransactionById,
  updateTransactionStatus,
  processRefund,
  getUserTransactions,
  getTransactionStats,
} from "../controllers/transaction.controller.js";

import {
  validateCreateTransaction,
  validateUpdateTransactionStatus,
  validateProcessRefund,
  validateTransactionQuery,
  validateTransactionStats,
} from "../validators/transaction.validator.js";

import { authorize } from "../middlewares/auth.middleware.js";

const router = express.Router();

// Apply authorization middleware to all routes
router.use(authorize(["admin", "super_admin"]));

// Create a new transaction
router.post("/", validateCreateTransaction, createTransaction);

// Get all transactions with filtering and pagination
router.get("/", validateTransactionQuery, getTransactions);

// Get transaction statistics
router.get("/stats", validateTransactionStats, getTransactionStats);

// Get user's transaction history
router.get("/user/:userId", validateTransactionQuery, getUserTransactions);

// Get a transaction by ID
router.get("/:id", getTransactionById);

// Update transaction status (for payment gateway callbacks)
router.patch("/:id/status", validateUpdateTransactionStatus, updateTransactionStatus);

// Process refund
router.post("/:id/refund", validateProcessRefund, processRefund);

export default router;
