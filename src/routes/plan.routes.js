import express from "express";
import {
  createPlan,
  getPlans,
  getPlanById,
  getVisiblePlans,
  getPlansByType,
  getPricingPlans,
  getRecommendedPlan,
  validatePlanChange,
  initializeDefaultPlans,
  updatePlan,
  deletePlan,
} from "../controllers/plan.controller.js";
import { validatePlan } from "../validators/plan.validator.js";
import { protect, authorize } from "../middlewares/auth.middleware.js";
import {
  sanitizePlanInput,
  sanitizeQueryParams,
  sanitizeUrlParams
} from "../middlewares/sanitization.middleware.js";

const router = express.Router();

// Public routes (no authentication required)
// GET /api/plans/pricing - Get pricing plans for public pricing page (includes Enterprise)
router.get("/pricing", getPricingPlans);

// GET /api/plans/visible - Get visible plans for pricing page
router.get("/visible", getVisiblePlans);

// GET /api/plans/type/:type - Get plans by type (individual, startup, etc.)
router.get("/type/:type", getPlansByType);

// GET /api/plans - Get all plans (with filtering)
router.get("/", sanitizeQueryParams, getPlans);

// GET /api/plans/:id - Get a single plan by ID
router.get("/:id", getPlanById);

// User-specific routes (authentication required)
router.use(protect);

// GET /api/plans/recommend - Get recommended plan for current user
router.get("/recommend", getRecommendedPlan);

// POST /api/plans/validate-change - Validate plan change for current user
router.post("/validate-change", validatePlanChange);

// Admin routes
router.use(authorize(["admin", "super_admin"]));

// POST /api/plans/initialize - Initialize default plans (admin only)
router.post("/initialize", initializeDefaultPlans);

// POST /api/plans - Create a new plan (admin only)
router.post("/", sanitizePlanInput, validatePlan, createPlan);

// PUT /api/plans/:id - Update a plan by ID (admin only)
router.put("/:id", sanitizeUrlParams, sanitizePlanInput, validatePlan, updatePlan);

// DELETE /api/plans/:id - Delete a plan by ID (admin only)
router.delete("/:id", sanitizeUrlParams, deletePlan);

export default router;
