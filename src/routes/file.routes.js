import express from "express";
import { protect, authorize } from "../middlewares/auth.middleware.js";
import { validateFile, validateFileUpdate } from "../validators/file.validator.js";
import {
  createFile,
  searchFileSystem,
  getItemById,
  updateItem,
  deleteItems,
  permanentDeleteItems,
  restoreItems,
  getFilesByType,
} from "../controllers/gallery.controller.js";

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Public routes (authenticated users)
router.get("/search", searchFileSystem); // Search files
router.get("/by-type", getFilesByType); // Get files by type/MIME type
router.get("/:id", getItemById); // Get file by ID

// Admin/Super Admin routes
router.use(authorize(["admin", "super_admin"]));

// File management
router.post("/", validateFile, createFile); // Create/upload file
router.put("/:id", validateFileUpdate, updateItem); // Update file
router.delete("/soft-delete", deleteItems); // Soft delete files
router.delete("/permanent-delete", permanentDeleteItems); // Permanently delete files
router.post("/restore", restoreItems); // Restore deleted files

export default router;
