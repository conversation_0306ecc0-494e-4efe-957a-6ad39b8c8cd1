import express from "express";
import {
  createFolder,
  createFile,
  getFolderContents,
  searchFileSystem,
  getItemById,
  updateItem,
  deleteItems,
  permanentDeleteItems,
  restoreItems,
  getFilesByType,
} from "../controllers/gallery.controller.js";
import { protect, authorize } from "../middlewares/auth.middleware.js";
import { validateFolder, validateFolderUpdate } from "../validators/folder.validator.js";
import { validateFile, validateFileUpdate } from "../validators/file.validator.js";

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Public routes (authenticated users)
router.get("/folder/:folderId/contents", getFolderContents); // Get folder contents
router.get("/search", searchFileSystem); // Search files and folders
router.get("/files/by-type", getFilesByType); // Get files by MIME type
router.get("/:id", getItemById); // Get item by ID

// Admin/Super Admin routes
router.use(authorize(["admin", "super_admin"]));

// Folder management
router.post("/folder", validateFolder, createFolder); // Create folder

// File management
router.post("/file", validateFile, createFile); // Create/upload file

// Item management
router.put("/:id", updateItem); // Update item (needs dynamic validation)
router.delete("/soft-delete", deleteItems); // Soft delete items
router.delete("/permanent-delete", permanentDeleteItems); // Permanently delete items
router.post("/restore", restoreItems); // Restore deleted items

export default router;
