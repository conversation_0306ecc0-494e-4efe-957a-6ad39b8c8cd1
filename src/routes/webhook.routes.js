import express from "express";
import {
  handleWebhook,
  handleRazorpayWebhook,
  handleLemonSqueezyWebhook,
  handlePaddleWebhook,
  getWebhookStatus,
  testWebhook,
  webhookHealthCheck,
  getWebhookLogs,
} from "../controllers/webhook.controller.js";
import {
  webhookRateLimit,
  rawBodyParser,
  validateWebhook,
  logWebhook,
  webhookErrorHandler,
} from "../middlewares/webhook.middleware.js";
import { protect, authorize } from "../middlewares/auth.middleware.js";

const router = express.Router();

// Apply rate limiting to all webhook routes
router.use(webhookRateLimit);

// Apply raw body parser for signature verification
router.use(rawBodyParser);

// Apply webhook logging
router.use(logWebhook);

// Public webhook endpoints (no auth required)
// These are called by payment gateways

/**
 * @route POST /api/webhooks/razorpay
 * @desc Handle Razorpay webhooks
 * @access Public (with signature verification)
 */
router.post(
  "/razorpay",
  validateWebhook("razorpay"),
  handleRazorpayWebhook
);

/**
 * @route POST /api/webhooks/lemonsqueezy
 * @desc Handle LemonSqueezy webhooks
 * @access Public (with signature verification)
 */
router.post(
  "/lemonsqueezy",
  validateWebhook("lemonsqueezy"),
  handleLemonSqueezyWebhook
);

/**
 * @route POST /api/webhooks/paddle
 * @desc Handle Paddle webhooks
 * @access Public (with signature verification)
 */
router.post(
  "/paddle",
  validateWebhook("paddle"),
  handlePaddleWebhook
);

/**
 * @route POST /api/webhooks/:gateway
 * @desc Generic webhook handler for any supported gateway
 * @access Public (with signature verification)
 */
router.post(
  "/:gateway",
  (req, res, next) => {
    const { gateway } = req.params;
    return validateWebhook(gateway)(req, res, next);
  },
  handleWebhook
);

// Protected endpoints (require authentication)

/**
 * @route GET /api/webhooks/status
 * @desc Get webhook system status
 * @access Private (Admin only)
 */
router.get(
  "/status",
  protect,
  authorize(["admin", "super_admin"]),
  getWebhookStatus
);

/**
 * @route GET /api/webhooks/health
 * @desc Webhook health check
 * @access Public
 */
router.get("/health", webhookHealthCheck);

/**
 * @route GET /api/webhooks/logs
 * @desc Get webhook processing logs
 * @access Private (Admin only)
 */
router.get(
  "/logs",
  protect,
  authorize(["admin", "super_admin"]),
  getWebhookLogs
);

/**
 * @route POST /api/webhooks/test/:gateway
 * @desc Test webhook processing (development only)
 * @access Private (Admin only)
 */
router.post(
  "/test/:gateway",
  protect,
  authorize(["admin", "super_admin"]),
  testWebhook
);

// Apply webhook error handler
router.use(webhookErrorHandler);

export default router;
