/**
 * System Health Routes
 * Routes for system health monitoring and metrics
 */

import express from "express";
import {
  getSystemHealth,
  getDatabaseHealth,
  getSystemMetrics
} from "../controllers/system.controller.js";
import { protect, authorize } from "../middlewares/auth.middleware.js";

const router = express.Router();

/**
 * @route GET /api/system/health
 * @desc Get comprehensive system health information
 * @access Public
 */
router.get("/health", getSystemHealth);

/**
 * @route GET /api/system/database
 * @desc Get database health information
 * @access Private (Admin only)
 */
router.get("/database", protect, authorize(["admin", "super_admin"]), getDatabaseHealth);

/**
 * @route GET /api/system/metrics
 * @desc Get system performance metrics
 * @access Private (Admin only)
 */
router.get("/metrics", protect, authorize(["admin", "super_admin"]), getSystemMetrics);

export default router;
