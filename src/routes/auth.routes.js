// authRoutes.js
import express from "express";
import passport from "passport";
import {
  registerUser,
  loginUser,
  verifyEmail,
  forgotPassword,
  resetPassword,
  getCurrentUser,
  logoutUser,
  logoutAllDevices,
  googleAuth,
  googleCallback,
  githubAuth,
  githubCallback,
  resendEmailVerification,
} from "../controllers/auth.controller.js";
import { protect } from "../middlewares/auth.middleware.js";
import { authRateLimit } from "../middlewares/rateLimiting.middleware.js";
import {
  registerValidator,
  loginValidator,
  forgotPasswordValidator,
  resetPasswordValidator,
  verifyEmailValidator,
} from "../validators/auth.validator.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

const router = express.Router();

// Apply auth rate limiting to sensitive routes
router.post(
  "/register",
  authRateLimit,
  registerValidator,
  validateRequest,
  registerUser
);
router.post(
  "/login",
  authRateLimit,
  loginValidator,
  validateRequest,
  loginUser
);
router.post(
  "/forgot-password",
  authRateLimit,
  forgotPasswordValidator,
  validateRequest,
  forgotPassword
);
router.post(
  "/reset-password",
  authRateLimit,
  resetPasswordValidator,
  validateRequest,
  resetPassword
);
router.post(
  "/verify-email",
  authRateLimit,
  verifyEmailValidator,
  validateRequest,
  verifyEmail
);

// Other routes with lighter rate limiting can use apiRateLimit
router.get("/me", protect, getCurrentUser);
router.post("/logout", protect, logoutUser);
router.post("/logout-all", protect, logoutAllDevices);
router.post("/resend-verification", protect, resendEmailVerification);

// Google OAuth routes
router.get("/google", authRateLimit, googleAuth);
router.get("/google/callback", authRateLimit, googleCallback);

// GitHub OAuth routes
router.get("/github", authRateLimit, githubAuth);
router.get("/github/callback", authRateLimit, githubCallback);

export default router;
