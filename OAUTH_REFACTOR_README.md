# OAuth Authentication System Refactor

## Overview

This refactor modernizes the authentication system to support multiple OAuth providers (Google, GitHub) and improves the overall architecture.

## Key Changes

### 1. User Model (`src/models/user.model.js`)

#### Removed Fields:
- `googleId` - Replaced with `oauthProviders` array
- `provider` - No longer needed with new OAuth system
- `is_email_verified` - Renamed to `isEmailVerified` for consistency

#### Added Fields:
- `oauthProviders` - Array to support multiple OAuth providers
  ```javascript
  oauthProviders: [
    {
      provider: { type: String, enum: ["google", "github"], required: true },
      provider_id: { type: String, required: true },
      linked_at: { type: Date, default: Date.now },
    },
  ]
  ```

#### New Methods:
- `hasOAuthProvider(provider)` - Check if user has specific OAuth provider
- `linkOAuthProvider(provider, providerId)` - Link new OAuth provider
- `unlinkOAuthProvider(provider)` - Unlink OAuth provider
- `getOAuthProvider(provider)` - Get OAuth provider info
- `canAuthenticateWithPassword()` - Check if user can auth with password
- `getPrimaryAuthMethod()` - Get primary authentication method

### 2. Passport Configuration (`src/config/passport.js`)

#### Enhanced Features:
- **Unified OAuth handling** - Single `linkOrCreateUser` function for both Google and GitHub
- **Smart username generation** - Automatic username generation with collision handling
- **Improved error handling** - Better logging and error management
- **Email verification** - OAuth emails are automatically marked as verified

#### Supported Providers:
- **Google OAuth** - Profile and email scope
- **GitHub OAuth** - User email scope

### 3. Authentication Controllers (`src/controllers/auth.controller.js`)

#### New OAuth Controllers:
- `googleAuth` - Initiate Google OAuth flow
- `googleCallback` - Handle Google OAuth callback
- `githubAuth` - Initiate GitHub OAuth flow  
- `githubCallback` - Handle GitHub OAuth callback

#### Enhanced Features:
- **State parameter support** - For redirect handling
- **Improved error handling** - Better user experience on auth failures
- **Token management** - Consistent JWT token generation

### 4. JWT Utilities (`src/utils/jwt.js`)

#### New Functions:
- `generateAccessAndRefreshTokens(userId, sessionData)` - OAuth token generation
- `setAccessTokenCookie(res, accessToken)` - Cookie management helper

### 5. Routes (`src/routes/auth.routes.js`)

#### Updated OAuth Routes:
```javascript
// Google OAuth
router.get("/google", authRateLimit, googleAuth);
router.get("/google/callback", authRateLimit, googleCallback);

// GitHub OAuth  
router.get("/github", authRateLimit, githubAuth);
router.get("/github/callback", authRateLimit, githubCallback);
```

## Environment Variables

Add these environment variables for GitHub OAuth:

```env
# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Frontend URL (for redirects)
CLIENT_URL=http://localhost:3000
# or
FRONTEND_URL=http://localhost:3000
```

## Migration

### Automatic Migration Script

Run the migration script to update existing users:

```bash
cd designbyte-server
node src/scripts/migrate-users.js
```

The migration script will:
1. Convert existing Google OAuth users to new `oauthProviders` format
2. Rename `is_email_verified` to `isEmailVerified`
3. Remove deprecated `provider` and `googleId` fields
4. Preserve all existing user data

### Manual Migration (if needed)

If you prefer manual migration, update existing users in MongoDB:

```javascript
// Update Google OAuth users
db.users.updateMany(
  { provider: "google", googleId: { $exists: true } },
  {
    $push: {
      oauthProviders: {
        provider: "google",
        provider_id: "$googleId",
        linked_at: new Date()
      }
    },
    $unset: { provider: "", googleId: "" },
    $rename: { is_email_verified: "isEmailVerified" }
  }
);

// Update email users
db.users.updateMany(
  { provider: "email" },
  {
    $unset: { provider: "" },
    $rename: { is_email_verified: "isEmailVerified" }
  }
);
```

## Security Improvements

1. **Rate limiting** - OAuth routes now have rate limiting
2. **Better error handling** - Prevents information leakage
3. **Session management** - Improved token and session handling
4. **Multiple auth methods** - Users can link multiple OAuth providers

## Usage Examples

### Frontend Integration

```javascript
// Google OAuth
window.location.href = '/api/v1/auth/google?clientRedirect=' + encodeURIComponent(window.location.origin);

// GitHub OAuth  
window.location.href = '/api/v1/auth/github?clientRedirect=' + encodeURIComponent(window.location.origin);
```

### Check User OAuth Providers

```javascript
// In your API routes
const user = await User.findById(userId);

// Check if user has Google linked
if (user.hasOAuthProvider('google')) {
  console.log('User has Google account linked');
}

// Get all linked providers
const providers = user.oauthProviders.map(oauth => oauth.provider);
console.log('Linked providers:', providers);
```

## Testing

1. **Test OAuth flows** in development
2. **Verify migration** completed successfully
3. **Check existing users** can still authenticate
4. **Test new OAuth providers** work correctly

## Breaking Changes

⚠️ **Important**: This refactor includes breaking changes to the user schema. Make sure to:

1. **Backup your database** before running migration
2. **Test in development** environment first
3. **Update frontend code** if it references old fields
4. **Run migration script** on production after testing

## Support

For any issues with the migration or new OAuth system, check:

1. **Migration script logs** for any failed user updates
2. **Environment variables** are correctly set
3. **OAuth app configurations** in Google/GitHub consoles
4. **Database indexes** if you encounter performance issues
