# Flexible Subscription System Documentation

## Overview

This subscription system is designed to work with any payment gateway through webhooks with minimal configuration changes. The system is built with a modular architecture that allows easy integration of new payment gateways.

## Architecture

### Core Components

1. **Webhook Handler Architecture** (`src/services/webhooks/WebhookHandler.js`)
   - Abstract base class for all payment gateway handlers
   - Standardized event types and data structures
   - Webhook registry for managing multiple gateways

2. **Subscription Service** (`src/services/subscription/SubscriptionService.js`)
   - Gateway-independent business logic
   - Handles user creation, subscription activation, cancellation, etc.
   - Manages transaction creation and user state updates

3. **Gateway-Specific Handlers**
   - `RazorpayWebhookHandler.js` - Razorpay integration
   - `LemonSqueezyWebhookHandler.js` - LemonSqueezy integration (example)
   - `PaddleWebhookHandler.js` - Paddle integration (example)

4. **Security & Validation** (`src/middlewares/webhook.middleware.js`)
   - Signature verification
   - Rate limiting
   - Idempotency handling
   - Request logging

## Features

### ✅ Implemented Features

- **Multi-Gateway Support**: Razorpay, LemonSqueezy, Paddle (easily extensible)
- **Webhook Security**: Signature verification, rate limiting, idempotency
- **User Management**: Automatic user creation from webhooks
- **Subscription Lifecycle**: Activation, renewal, cancellation, expiration
- **Transaction Tracking**: Complete transaction history with metadata
- **Flexible Metadata**: JSON storage for gateway-specific data
- **Retry Logic**: Automatic retry for failed webhook processing
- **Lifetime Subscriptions**: Support for one-time lifetime payments
- **Plan Management**: Flexible plan structure with multiple durations

### 🔄 Subscription States

- `active` - User has an active subscription
- `inactive` - User subscription is not active
- `cancelled` - User cancelled their subscription
- `expired` - Subscription has expired
- `pending` - Subscription is being processed
- `paused` - Subscription is temporarily paused

### 💳 Supported Payment Methods

- Credit/Debit Cards
- UPI (for Indian gateways)
- Bank Transfer
- Digital Wallets
- PayPal
- Free (100% coupon discounts)

## Setup Instructions

### 1. Environment Variables

```bash
# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret

# LemonSqueezy Configuration (optional)
LEMONSQUEEZY_WEBHOOK_SECRET=your_lemonsqueezy_webhook_secret

# Paddle Configuration (optional)
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret

# Database
MONGODB_URI=mongodb://localhost:27017/your_database
```

### 2. Webhook URLs

Configure these webhook URLs in your payment gateway dashboards:

- **Razorpay**: `https://yourdomain.com/api/webhooks/razorpay`
- **LemonSqueezy**: `https://yourdomain.com/api/webhooks/lemonsqueezy`
- **Paddle**: `https://yourdomain.com/api/webhooks/paddle`
- **Generic**: `https://yourdomain.com/api/webhooks/{gateway_name}`

### 3. Webhook Events

The system handles these standardized events:

- `subscription.created`
- `subscription.activated`
- `subscription.updated`
- `subscription.cancelled`
- `subscription.expired`
- `subscription.paused`
- `subscription.resumed`
- `payment.success`
- `payment.failed`
- `payment.pending`
- `payment.refunded`

## API Endpoints

### Webhook Endpoints

```
POST /api/webhooks/razorpay          # Razorpay webhooks
POST /api/webhooks/lemonsqueezy      # LemonSqueezy webhooks
POST /api/webhooks/paddle            # Paddle webhooks
POST /api/webhooks/:gateway          # Generic webhook handler
```

### Management Endpoints

```
GET  /api/webhooks/status            # System status (Admin only)
GET  /api/webhooks/health            # Health check
GET  /api/webhooks/logs              # Webhook logs (Admin only)
POST /api/webhooks/test/:gateway     # Test webhook (Dev only)
```

## Usage Examples

### 1. Razorpay Webhook Processing

When Razorpay sends a webhook, the system:

1. Verifies the webhook signature
2. Parses the Razorpay-specific payload
3. Converts it to standardized format
4. Processes the subscription logic
5. Updates user and transaction records

### 2. Adding a New Payment Gateway

To add a new gateway (e.g., Stripe):

1. Create `StripeWebhookHandler.js`:

```javascript
import { WebhookHandler, WEBHOOK_EVENTS } from './WebhookHandler.js';

export class StripeWebhookHandler extends WebhookHandler {
  constructor() {
    super('stripe');
  }

  verifySignature(payload, signature, secret) {
    // Implement Stripe signature verification
  }

  getEventType(payload) {
    // Map Stripe events to standard events
  }

  extractSubscriptionData(payload) {
    // Extract subscription data from Stripe payload
  }

  // ... implement other required methods
}
```

2. Register the handler in `src/services/webhooks/index.js`:

```javascript
import { stripeWebhookHandler } from './StripeWebhookHandler.js';

webhookRegistry.register('stripe', stripeWebhookHandler);
```

3. Add webhook route and middleware validation

## User Model Integration

The enhanced user model includes comprehensive subscription management:

```javascript
// Check subscription status
user.hasActiveSubscription()
user.isLifetimePro
user.isSubscriptionExpired()

// Manage subscriptions
await user.activateSubscription(plan, transaction)
await user.cancelSubscription(reason)
await user.renewSubscription(plan, transaction)

// Get subscription summary
const summary = user.getSubscriptionSummary()
```

## Transaction Model

Enhanced transaction model with flexible metadata:

```javascript
{
  user: ObjectId,
  plan: ObjectId,
  type: 'subscription_purchase',
  amount: 29.99,
  status: 'completed',
  paymentGateway: 'razorpay',
  userSubscriptionData: {
    previousTier: 'free',
    newTier: 'monthly',
    isUpgrade: true
  },
  metadata: {
    source: 'razorpay',
    customData: { /* flexible JSON data */ }
  }
}
```

## Security Features

- **Signature Verification**: All webhooks are verified using gateway-specific signatures
- **Rate Limiting**: Prevents webhook spam and abuse
- **Idempotency**: Prevents duplicate processing of the same webhook
- **Request Logging**: Complete audit trail of all webhook requests
- **Error Handling**: Graceful error handling with retry logic

## Testing

The system includes comprehensive testing:

- Webhook handler registration
- Signature verification
- User creation and subscription management
- Transaction creation and tracking
- Error handling and edge cases

## Production Deployment

### 1. Security Checklist

- [ ] Configure webhook secrets in environment variables
- [ ] Set up HTTPS for webhook endpoints
- [ ] Configure rate limiting
- [ ] Set up monitoring and alerting
- [ ] Test webhook signature verification

### 2. Monitoring

Monitor these metrics:

- Webhook processing success rate
- Response times
- Failed webhook attempts
- User subscription conversions
- Transaction volumes

### 3. Scaling Considerations

- Use Redis for webhook idempotency in multi-instance deployments
- Implement webhook queuing for high-volume scenarios
- Set up database indexing for performance
- Consider webhook retry mechanisms

## Support

For issues or questions:

1. Check webhook logs: `GET /api/webhooks/logs`
2. Verify webhook configuration: `GET /api/webhooks/status`
3. Test webhook processing: `POST /api/webhooks/test/:gateway`
4. Review transaction records in the database

## Future Enhancements

- [ ] Webhook queue system for high volume
- [ ] Advanced analytics and reporting
- [ ] Subscription upgrade/downgrade flows
- [ ] Proration handling
- [ ] Multi-currency support
- [ ] Subscription pause/resume functionality
- [ ] Advanced coupon system integration
