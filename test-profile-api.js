// Simple test script to verify profile API endpoints
// Run with: node test-profile-api.js

import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:5000/api/v1';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPass123',
  username: 'testuser',
  firstName: 'Test',
  lastName: 'User'
};

let authToken = '';

async function testRegisterUser() {
  console.log('🧪 Testing user registration...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ User registration successful');
      return true;
    } else {
      console.log('ℹ️ User might already exist:', data.message);
      return true; // Continue with login
    }
  } catch (error) {
    console.error('❌ Registration failed:', error.message);
    return false;
  }
}

async function testLoginUser() {
  console.log('🧪 Testing user login...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
      }),
    });

    const data = await response.json();
    
    if (response.ok && data.data?.token) {
      authToken = data.data.token;
      console.log('✅ User login successful');
      return true;
    } else {
      console.error('❌ Login failed:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    return false;
  }
}

async function testUpdateProfile() {
  console.log('🧪 Testing profile update...');
  
  const updateData = {
    firstName: 'Updated',
    lastName: 'Name',
    phoneNumber: '+1234567890',
  };

  try {
    const response = await fetch(`${API_BASE_URL}/profile`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(updateData),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Profile update successful');
      console.log('📄 Updated user:', {
        firstName: data.data.user.firstName,
        lastName: data.data.user.lastName,
        phoneNumber: data.data.user.phoneNumber,
      });
      return true;
    } else {
      console.error('❌ Profile update failed:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Profile update failed:', error.message);
    return false;
  }
}

async function testChangePassword() {
  console.log('🧪 Testing password change...');
  
  const passwordData = {
    currentPassword: testUser.password,
    newPassword: 'NewPass123',
  };

  try {
    const response = await fetch(`${API_BASE_URL}/profile/change-password`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(passwordData),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Password change successful');
      // Update test password for future tests
      testUser.password = passwordData.newPassword;
      return true;
    } else {
      console.error('❌ Password change failed:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Password change failed:', error.message);
    return false;
  }
}

async function testGetCurrentUser() {
  console.log('🧪 Testing get current user...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Get current user successful');
      console.log('📄 User data:', {
        email: data.data.user.email,
        firstName: data.data.user.firstName,
        lastName: data.data.user.lastName,
        phoneNumber: data.data.user.phoneNumber,
      });
      return true;
    } else {
      console.error('❌ Get current user failed:', data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Get current user failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Profile API Tests\n');
  
  // Test registration (or skip if user exists)
  await testRegisterUser();
  
  // Test login
  const loginSuccess = await testLoginUser();
  if (!loginSuccess) {
    console.log('❌ Cannot continue without authentication');
    return;
  }
  
  console.log(''); // Empty line for readability
  
  // Test profile update
  await testUpdateProfile();
  
  console.log(''); // Empty line for readability
  
  // Test password change
  await testChangePassword();
  
  console.log(''); // Empty line for readability
  
  // Test get current user (to verify changes)
  await testGetCurrentUser();
  
  console.log('\n🎉 Profile API tests completed!');
}

// Run the tests
runTests().catch(console.error);
