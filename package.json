{"name": "designbyte-server", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "migrate": "node src/scripts/migrate.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@upstash/redis": "^1.35.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.21.1", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.8.3", "node-cron": "^3.0.3", "passport": "^0.7.0", "passport-github": "^1.1.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "resend": "^4.6.0", "serverless-http": "^3.2.0", "slugify": "^1.6.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ua-parser-js": "^1.0.37", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.7"}}