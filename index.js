import connectDB from "./src/config/db.js";
import "dotenv/config";
import app from "./src/app.js";
import logger from "./src/utils/logger.js";
import serverless from "serverless-http";

const isProduction = process.env.NODE_ENV === "production";

if (!isProduction) {
  // Local development mode
  const startServer = async () => {
    try {
      await connectDB(); // Establish DB connection once at server startup
      const port = process.env.PORT || 3000;
      const server = app.listen(port, () => {
        logger.info(
          `DesignByte backend is running at http://localhost:${port} in ${process.env.NODE_ENV} mode`
        );
      });

      // Graceful shutdown for unhandled rejections
      process.on("unhandledRejection", (err) => {
        logger.error("Unhandled Rejection:", err.stack || err);
        server.close(() => process.exit(1));
      });
    } catch (err) {
      logger.error("Failed to start the server:", err.stack || err);
      process.exit(1);
    }
  };

  startServer();
} else {
  // Production mode for serverless
  const setupServerless = async () => {
    try {
      await connectDB(); // Connect to DB during Lambda initialization (cold start)
      logger.info("Database connection established for production");
      logger.info(
        `DesignByte backend is running in ${process.env.NODE_ENV} mode`
      );
    } catch (err) {
      logger.error("Failed to connect to the database:", err.stack || err);
      process.exit(1); // Exit the process for critical initialization errors
    }
  };

  setupServerless();
}

// Export the serverless handler
export const handler = serverless(app);
